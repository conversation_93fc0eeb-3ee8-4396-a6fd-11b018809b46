package com.mega.eloan.lms.cls.common;

import java.lang.reflect.Constructor;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.utils.BeanValidator;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ScoreCardLoan;
import com.mega.eloan.lms.base.constants.ScoreNotHouseLoan;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1151S01Page;
import com.mega.eloan.lms.cls.pages.CLSCommomPage;
import com.mega.eloan.lms.cls.panels.CLS1131S01Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S01Panel;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01D;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01F;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S01K;
import com.mega.eloan.lms.model.C101S01L;
import com.mega.eloan.lms.model.C101S01M;
import com.mega.eloan.lms.model.C101S01N;
import com.mega.eloan.lms.model.C101S01O;
import com.mega.eloan.lms.model.C101S01P;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C101S01V;
import com.mega.eloan.lms.model.C101S01W;
import com.mega.eloan.lms.model.C101S01X;
import com.mega.eloan.lms.model.C101S01Y;
import com.mega.eloan.lms.model.C101S01Z;
import com.mega.eloan.lms.model.C101S02A;
import com.mega.eloan.lms.model.C101S02B;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01J;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;
import com.mega.eloan.lms.model.C160A01A;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C160M01C;
import com.mega.eloan.lms.model.C160M01D;
import com.mega.eloan.lms.model.C160M01E;
import com.mega.eloan.lms.model.C160S01A;
import com.mega.eloan.lms.model.C160S01B;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C160S01E;
import com.mega.eloan.lms.model.C160S01F;
import com.mega.eloan.lms.model.C160S01G;
import com.mega.eloan.lms.model.C900M01J;
import com.mega.eloan.lms.model.L120S03A;
import com.mega.eloan.lms.model.L120S06A;
import com.mega.eloan.lms.model.L120S06B;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M03A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02F;
import com.mega.eloan.lms.validation.group.Check;

// UPGRADETODO: JXL 轉換為 Apache POI - 導入新的 POI 類別
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;
// import jxl.Cell;
// import jxl.format.CellFormat;
// import jxl.write.WritableCellFormat;
// import jxl.write.WritableSheet;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * CLS共用
 * </pre>
 * 
 * @since 2012/11/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/20,Fantasy,new
 *          <li>2013/07/09.Fantasy "回復"改為"無調整"
 *          <li>2013/07/11,Rex,當為數字要轉成數值比對不然會有0.00和0的差別
 *          </ul>
 */
public class ClsUtil {

	private static final Logger logger = LoggerFactory.getLogger(ClsUtil.class);

	public static final Class<?>[] C160Class = { C160A01A.class,
			C160M01A.class, C160M01B.class, C160M01C.class, C160M01D.class,
			C160M01E.class, C160S01A.class, C160S01B.class, C160S01C.class,
			C160S01E.class, C160S01F.class };

	public static final Class<?>[] C160SClass = { C160S01A.class,
			C160S01B.class, C160S01C.class, C160S01E.class, C160S01F.class };

	public static final Class<?>[] C101CheckClass = { C101S01A.class,
			C101S01B.class, C101S01C.class, C101S01D.class, C101M01A.class };

	public static final String[] typeNA = { // "isQdata4",
			"isQdata14", "isQdata8", "isQdata9", "isQdata10", "isQdata11", "isQdata13",
			"isQdata12", "isQdata15", "isQdata17", "isQdata18" };

//	public static final Properties prop = MessageBundleScriptCreator.getComponentResource(CLS1131S01GPanel.class);
//	private static final Properties prop_CLS1131S01QPanel = MessageBundleScriptCreator.getComponentResource(CLS1131S01QPanel.class);
	
	public static final String PROD_UI_SEQ = "uiSeq";
	public static final String JSON_OUTPUT_PARAM_SHOW_S01R = "show_s01r";
	
	public static final String[] C122_C120S01A_COLS ={
		"birthday", "edu"
		, "marry", "child"
		, "coTel", "fTel"
		, "mTel", "email"
		, "fTarget"   //"fCity", "fZip", "fAddr"
		, "coCity", "coZip", "coAddr", "coTarget"  
		, "houseStatus", "cmsStatus"
	};
	public static final String[] C122_C120S01B_COLS ={
		"comName"
		, "comCity", "comZip", "comAddr", "comTarget" 
		, "comTel"
		, "jobType1", "jobType2", "jobTitle"
		, "seniority", "payAmt"
		, "othType", "othAmt"					
	};
	public static final String[] C122_C120S01C_COLS ={
		"isPeriodFund", "busi"
	};
	public static final String[] C122_C120S01A_COLS_Prod69 ={
		"birthday", "edu"
		, "marry", "child"
		//, "coTel" 把網頁上「住家電話(非必填)」借放在「通訊電話」，可能「通訊地址、電話」都留「公司」
		, "mTel", "email"
		, "fCity", "fZip", "fAddr", "fTarget"   
		, "coCity", "coZip", "coAddr", "coTarget"  
		, "houseStatus"
	};
	public static final String[] C122_C120S01B_COLS_Prod69 ={
		"comName"
		, "comTel"
		, "jobTitle"
		, "seniority", "payAmt"
		, "othType", "othAmt"					
	};

	public static final String[] C122_C120S01A_COLS_PLOAN ={
		"birthday", "edu"
		, "marry", "child"
		//, "coTel" 把網頁上「住家電話(非必填)」借放在「通訊電話」，可能「通訊地址、電話」都留「公司」
		, "mTel", "email"
		, "fCity", "fZip", "fAddr", "fTarget"   
		, "coCity", "coZip", "coAddr", "coTarget"  
		, "houseStatus"
	};
	public static final String[] C122_C120S01B_COLS_PLOAN ={
		"comName"
		, "comTel"
		, "jobTitle"
		, "seniority", "payAmt"
		, "othType", "othAmt"					
	};	
//	private static Map<String, String> country = null;	
//	public static String test(String key){		
//		if(country == null){
//			country = new HashMap<String, String>();
//			country.put("桃園縣", "桃園市");
//		}		
//		if(country.containsKey(key)){
//			return null;
//		}		
//	}
	public static final String [] HVILLAGES = {
		"桃園市","中壢市","大溪鎮","楊梅市","蘆竹市","大園鄉","龜山鄉","八德市","龍潭鄉","平鎮市","新屋鄉","觀音鄉","復興鄉"
	};
	
	public static final String [] HTOWNS = {
		"九斗村","九龍村","八德村","三水村","三民村","三石村","三光村","三坑村","三和村","三和村","三林村","上大村","上林村",
		"上華村","下田村","下埔村","大平村","大同村","大同村","大坑村","大坡村","大海村","大堀村","大崗村","大湖村","大華村",
		"大園村","大潭村","山頂村","山福村","山德村","中山村","中正村","中興村","中興村","五權村","內海村","公西村","文化村",
		"北港村","北興村","永安村","永興村","永興村","田心村",	"白玉村","石牌村","石磊村","圳頭村","百年村","竹圍村","坑尾村",
		"沙崙村","赤欄村","佳安村","兔坑村","和平村","幸福村","東明村","東興村","武威村","武漢村","社子村","金湖村","長庚村",
		"長興村","保生村","保障村","南上村","南美村","南港村","奎輝村","建林村","後庄村","後厝村","後湖村","凌雲村","埔心村",
		"埔頂村","海口村","烏林村","烏樹林村","草新村","草漯村","迴龍村","高平村","高原村","高義村","崙坪村","望間村","深圳村",
		"清華村","笨港村","蚵間村","陸光村","富林村","富林村","富源村","菓林村","華陵村","黃唐村","塔腳村","新生村","新坡村",
		"新屋村","新路村","新興村","新興村","新嶺村","楓樹村",	"溪海村","義盛村","聖德村","福源村","精忠村","廣福村","廣興村",
		"槺榔村","樂善村","樹林村","橫峰村","澤仁村","頭洲村","龍星村","龍祥村","龍華村","龍壽村","龍潭村","龜山村","嶺頂村",
		"霞雲村","舊路村","藍埔村","羅浮村","觀音村"
	};
	
	public static final String CLS1131_QUERY_SINGLE_C101M01A_1 = "C101M01AJson";	
	public static final String CLS1131_QUERY_SINGLE_C101M01A_29 = "C101M01AJson2";
	
	public static final String C160M01A_RPTID_V20190920 = "V20190920";
	
	public static Map<String, Boolean> getOrderBy_uiSeq_seq(){
		Map<String, Boolean> orderByMap = new HashMap<String, Boolean>();
		orderByMap.put("uiSeq", false);
		orderByMap.put("seq", false);
		return orderByMap;
	}
	
	
	private static String _getAdjustStatus(String adjustStatus, String raw_grade1, String raw_grade2) {
		String grade2 = Util.trim(raw_grade2);
		if(Util.equals("1", adjustStatus)){
			return "調升"+grade2+"等";
		}else if(Util.equals("2", adjustStatus)){
			return "調降"+grade2+"等";
		}else if(Util.equals("3", adjustStatus)){	
			return "無調整";
		}else{
			// 表示有評等過
			if (Util.isNotEmpty(Util.trim(raw_grade1))) {
				return "無調整";
			}
			return "";
		}	
	}
	/**
	 * 取得調整狀態
	 * 
	 * @param status
	 * @param grade
	 * @return
	 */
	public static String getAdjustStatus(C101S01G model) { //房貸
		if (model != null) {
			if(Util.equals(model.getVarVer(), ClsScoreUtil.V3_0_HOUSE_LOAN)){ 
				//房貸模型3.0，不可調整評等，顯示(不得調整)
				return "(不得調整)";	
			}else{
				return _getAdjustStatus(model.getAdjustStatus(), model.getGrade1(), model.getGrade2());	
			}
		}
		return "";
	}

	/**
	 * 取得調整狀態
	 * 
	 * @param status
	 * @param grade
	 * @return
	 */
	public static String getAdjustStatus(C101S01Q model) throws CapException { //非房貸
		if (model != null) {
			if(Util.equals(model.getVarVer(), ClsScoreUtil.V4_0_NOT_HOUSE_LOAN)){ 
				//非房貸模型4.0，不可調整評等，顯示(不得調整)
				return "(不得調整)";	
			}else{
				return _getAdjustStatus(model.getAdjustStatus(), model.getGrade1(), model.getGrade2());	
			}
		}
		return "";
	}
	
	public static String getAdjustStatus(C101S01R model) throws CapException { //專案信貸非團體
		if (model != null) {
			if(Util.equals(model.getVarVer(), ClsScoreUtil.V4_0_CARD_LOAN)){ 
				//專案信貸(非團體)模型4.0，不可調整評等，顯示(不得調整)
				return "(不得調整)";	
			}else{
				return _getAdjustStatus(model.getAdjustStatus(), model.getGrade1(), model.getGrade2());	
			}
		}
		return "";
	}
	
	/**
	 * 取得幾個月前的日期
	 * 
	 * @param value
	 * @return
	 */
	public static String getDateOfMonthsAgo(int value) {
		Date today = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(today);
		calendar.add(Calendar.MONTH, -value);
		TWNDate.toAD(calendar.getTime());
		return TWNDate.toAD(calendar.getTime());
	}

	/**
	 * key:c101s01q 的欄位名
	 * val:c101s01a 的欄位名
	 * @return
	 */
	public static Map<String, String> map_c101s01q_c101s01a_V1_0(){
		Map<String, String> r = new HashMap<String, String>();
		r.put(ScoreNotHouseLoan.column.非房貸學歷, ClsConstants.ratingFactor_Q_V1_0.C101S01A.非房貸學歷);
		return r;
	}
	
	/**
	 * key:c101s01Q 的欄位名
	 * val:c101s01b 的欄位名
	 * @return
	 */
	public static Map<String, String> map_c101s01q_c101s01b_V1_0(){
		Map<String, String> r = new HashMap<String, String>();
		r.put(ScoreNotHouseLoan.column.個人年收入, ClsConstants.ratingFactor_Q_V1_0.C101S01B.個人年收入);
		r.put(ScoreNotHouseLoan.column.年資, ClsConstants.ratingFactor_Q_V1_0.C101S01B.年資);
		return r;
	}
	
	/**
	 * key:c101s01Q 的欄位名
	 * val:c101s01b 的欄位名
	 * @return
	 */
	public static void set_c101s01q_factor_V2_0(JSONObject result, JSONObject uiC101S01BForm){
		result.put(ScoreNotHouseLoan.column.個人年所得, get_pIncome_from_uiC101S01BForm(uiC101S01BForm));
		result.put(ScoreNotHouseLoan.column.年資, uiC101S01BForm.get(ClsConstants.ratingFactor_Q_V2_0.C101S01B.年資));
	}
	
	public static void set_c101s01q_factor_V2_1(JSONObject result, JSONObject uiC101S01BForm){
		result.put(ScoreNotHouseLoan.column.個人年所得, get_pIncome_from_uiC101S01BForm(uiC101S01BForm));
		result.put(ScoreNotHouseLoan.column.年資, uiC101S01BForm.get(ClsConstants.ratingFactor_Q_V2_1.C101S01B.年資));
	}	
	
	public static void set_c101s01r_factor_V2_1(JSONObject result, JSONObject uiC101S01BForm){
		result.put(ScoreCardLoan.column.個人年所得, get_pIncome_from_uiC101S01BForm(uiC101S01BForm));
		result.put(ScoreCardLoan.column.年資, uiC101S01BForm.get(ClsConstants.ratingFactor_R_V2_1.C101S01B.年資));
	}
	
	public static BigDecimal get_pIncome_from_uiC101S01BForm(JSONObject uiC101S01BForm){
		BigDecimal pIncome = get_BigDecimal(uiC101S01BForm, "payAmt").add( 
				get_BigDecimal(uiC101S01BForm, "othAmt"));
		return pIncome;
	}
	
	public static BigDecimal get_BigDecimal(JSONObject formObj, String key){
		return CrsUtil.parseBigDecimal(MapUtils.getString(formObj, key));
	}
	
	/**
	 * 將 className + "Form" 轉成 JSONObject
	 * 
	 * @param value
	 * @return
	 * @throws CapException
	 */
	public static JSONObject getJson(PageParameters params, Class<?> clazz)
			throws CapException {
		JSONObject result = null;

		if (params != null && clazz != null) {
			String className = Util.trim(clazz.getSimpleName());
			String form = Util.trim(params.getString(className + "Form"));
			if (Util.isNotEmpty(form))
				result = DataParse.toJSON(form);
		}
		return result == null ? new JSONObject() : result;
	}

	/**
	 * 取得關係類別
	 * 
	 * @param mainId
	 */
	public static String checkData(C160S01D model, Map<String, String> ntCode_map) {
		StringBuilder result = new StringBuilder();
		if (model != null) {
			// model check
			if (!BeanValidator.isValid(model, Check.class)) {
				result.append(BeanValidator.getValidMsg(model,
						CLS1161S01Panel.class, Check.class));
			}
		}
		if(true){
			if(Util.isNotEmpty(Util.trim(model.getCustId()))){
				String country = Util.trim(model.getNtCode());
				 if(Util.isEmpty(country)){
					 result.append("請輸入國別").append("<br/>");	 
				 }else{
					 if(!ntCode_map.containsKey(country)){
						 result.append("國別["+country+"]不存在").append("<br/>");		 
					 }
				 }				
			}
			if(Util.isNotEmpty(Util.trim(model.getRId1()))){
				String country = Util.trim(model.getRNtCode1());
				if(Util.isEmpty(country)){
					 result.append("保人1身分證字號有值，請輸入國別").append("<br/>");	 
				 }else{
					 if(!ntCode_map.containsKey(country)){
						 result.append("保人1國別["+country+"]不存在").append("<br/>");		 
					 }
				 }
				
				if(Util.isEmpty(Util.trim(model.getRKindD1()))){
					 result.append("保人1身分證字號有值，請輸入與借款人關係").append("<br/>");	 
				}
				if(Util.isEmpty(Util.trim(model.getRType1()))){
					 result.append("保人1身分證字號有值，請輸入保證性質1").append("<br/>");	 
				}
			}
			if(Util.isNotEmpty(Util.trim(model.getRId2()))){
				String country = Util.trim(model.getRNtCode2());
				if(Util.isEmpty(country)){
					 result.append("保人2身分證字號有值，請輸入國別").append("<br/>");	 
				 }else{
					 if(!ntCode_map.containsKey(country)){
						 result.append("保人2國別["+country+"]不存在").append("<br/>");		 
					 }
				 }
				
				if(Util.isEmpty(Util.trim(model.getRKindD2()))){
					 result.append("保人2身分證字號有值，請輸入與借款人關係").append("<br/>");	 
				}
				if(Util.isEmpty(Util.trim(model.getRType2()))){
					 result.append("保人2身分證字號有值，請輸入保證性質2").append("<br/>");	 
				}
			}
			if(Util.isNotEmpty(Util.trim(model.getAccNo()))){
				String accNo = Util.trim(model.getAccNo());
				if(accNo.length()<11){
					result.append("進帳帳號有值，但長度不足11位").append("<br/>");	
				}				
			}
			if(Util.isNotEmpty(Util.trim(model.getAtpayNo()))){
				String atpayNo = Util.trim(model.getAtpayNo());
				if(atpayNo.length()<11){
					result.append("扣帳帳號有值，但長度不足11位").append("<br/>");	
				}				
			}
		}
		return result.toString().replaceAll("<br/>", "\r\n");		
	}

	/**
	 * 取得Excel之欄位格式
	 * 
	 * @param mainId
	 */
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改 getCellFormat 方法使用 POI CellStyle
	public static CellStyle getCellFormat(Cell cell) {
		CellStyle result = null;
		if (cell != null) {
			// POI 中的儲存格樣式取得方式
			result = cell.getCellStyle();
		}
		return result;
	}

	// UPGRADETODO: JXL 轉換為 Apache POI - 修改 formatValue 方法支援 POI Cell
	public static String formatValue(String key, Cell cell) {
		String result = "";
		if (key != null && cell != null) {
			// POI 中取得儲存格內容的方式
			String cellValue = "";
			switch (cell.getCellType()) {
				case STRING:
					cellValue = cell.getStringCellValue();
					break;
				case NUMERIC:
					cellValue = String.valueOf((long) cell.getNumericCellValue());
					break;
				case BOOLEAN:
					cellValue = String.valueOf(cell.getBooleanCellValue());
					break;
				case FORMULA:
					cellValue = cell.getCellFormula();
					break;
				default:
					cellValue = "";
					break;
			}
			String[] values = Util.trim(cellValue).split(" ");
			result = values[0];
			// if (key.contains("Date"))
			// return result.replaceAll("/", "-");
		}
		return result;
	}

	/**
	 * 由Excel取得個金動審表匯入主檔資料
	 * 
	 * @param sheet
	 * @return
	 * @throws CapException
	 */
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改 getC160m01fData 方法使用 POI Sheet
	public static JSONObject getC160m01fData(Sheet sheet,
			Properties prop) {
		JSONObject result = new JSONObject();

		int x = 1; // Excel 欄位 B (索0開始計數，所以B=1)
		for (int y = 1; y <= 25; y++) {
			String key = Util.trim(prop.getProperty("C160M01F."
					+ Util.addZeroWithValue(y, 2)));
			if (Util.isNotEmpty(key)) {
				// POI 中 row 和 column 都從 0 開始，sheet.getRow(y).getCell(x)
				Cell cell = sheet.getRow(y) != null ? sheet.getRow(y).getCell(x) : null;
				result.put(key, formatValue(key, cell));
			}
		}
		// dupNo 位於 D1(欄位 3，列 1)
		Cell dupNoCell = sheet.getRow(1) != null ? sheet.getRow(1).getCell(3) : null;
		result.put("dupNo", formatValue("dupNo", dupNoCell));

		return result;
	}

	/**
	 * 由Excel取得個金動審表匯入利率檔
	 * 
	 * @param sheet
	 * @return
	 * @throws CapException
	 */
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改 getC160s01gList 方法使用 POI Sheet
	public static List<C160S01G> getC160s01gList(String mainId,
			Sheet sheet, Properties prop) {
		List<C160S01G> result = new ArrayList<C160S01G>();

		int seq = 0;
		// 利率資料位於第 8-12 列（POI 中列號從 0 開始，所以是 8-12）
		for (int y = 8; y <= 12; y++) {
			JSONObject json = new JSONObject();
			// 欄位 1-7 對應 B-H 欄
			for (int x = 1; x <= 7; x++) {
				String key = Util.trim(prop.getProperty("C160S01G."
						+ Util.addZeroWithValue(x, 2)));
				if (Util.isNotEmpty(key)) {
					// POI 中取得儲存格的方式
					Cell cell = sheet.getRow(y) != null ? sheet.getRow(y).getCell(x) : null;
					String value = formatValue(key, cell);
					if (Util.isNotEmpty(value))
						json.put(key, value);
				}
			}
			// check have data
			if (!json.isEmpty()) {
				C160S01G c160s01g = new C160S01G();
				DataParse.toBean(json, c160s01g);
				c160s01g.setMainId(mainId);
				c160s01g.setSeq(++seq);
				result.add(c160s01g);
			}
		}

		return result;
	}

	/**
	 * 由Excel取得個金動審表匯入明細檔
	 * 
	 * @param sheet
	 * @return
	 * @throws CapException
	 */
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改 getC160s01dData 方法使用 POI Sheet
	public static JSONObject getC160s01dData(Sheet sheet,
			Properties prop, int y) {
		JSONObject result = new JSONObject();

		// 明細資料欄位 A-AB （從 0 到 27）
		for (int x = 0; x <= 27; x++) {
			String key = Util.trim(prop.getProperty("C160S01D."
					+ Util.addZeroWithValue(x, 2)));
			if (Util.isNotEmpty(key)) {
				// POI 中取得儲存格的方式
				Cell cell = sheet.getRow(y) != null ? sheet.getRow(y).getCell(x) : null;
				String value = formatValue(key, cell);
				if (Util.isNotEmpty(value))
					result.put(key, value);
			}
		}

		// 額度序號
		// if (result.containsKey("cntrNo") &&
		// Util.isEmpty(result.get("cntrNo")))
		// result.remove("cntrNo");

		return result;
	}

	/**
	 * 取得建物HTML
	 * 
	 * @param build
	 *            *
	 * 
	 *            <pre>
	 * JSONArray物件
	 * [{
	 * target:建物地址
	 * bldno:建號
	 * },....]
	 * 
	 * </pre>
	 * 
	 *            建物json字串
	 * @return
	 */
	public static String getBuildHtml(String build) {
		StringBuffer temp = new StringBuffer();
		if (Util.isEmpty(Util.trim(build))) {
			return "";
		}
		JSONArray jsons = JSONArray.fromObject(build);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLSCommomPage.class);
		for (int i = 0, total = jsons.size(); i < total; i++) {
			JSONObject data = JSONObject.fromObject(jsons.get(i));
			if (data == null || "null".equals(Util.trim(data))) {
				continue;
			}
			String target = Util.trim(data.get("target"));
			String bldno = Util.trim(data.get("bldno"));
			temp.append("<tr>");
			temp.append("<td>").append(target).append("</td>");
			temp.append("<td>").append(bldno).append("</td>");
			temp.append("</tr>");
		}
		if (temp.length() > 0) {
			StringBuffer berforHtml = new StringBuffer();
			berforHtml.append("<table class='tb2' style='width:100%'>");
			berforHtml.append("<tr>");
			// commonPage.ms01=建物地址
			// commonPage.ms02=建號
			berforHtml.append("<th style='width:70%'>")
					.append(prop.getProperty("commonPage.ms01"))
					.append("</th>");
			berforHtml.append("<th style='width:30%'>")
					.append(prop.getProperty("commonPage.ms01"))
					.append("</th>");
			berforHtml.append("</tr>");
			temp.insert(0, berforHtml);
			temp.append("</table >");
		}
		return temp.toString();
	}

	/**
	 * 取得AreaDetail
	 * 
	 * @param areaDetail
	 *            *
	 * 
	 *            <pre>
	 *  JSONArray物件
	 * [{
	 *  target:土地地址 
	 *  landNo:地號 
	 *  ttlNum:已敘做總戶數
	 *  ttlBal:已敘做總金額
	 *  },
	 * ....]
	 * </pre>
	 * 
	 *            土地json字串
	 * 
	 * @return
	 */
	public static String getAreaDetailHtml(String areaDetail) {
		StringBuffer temp = new StringBuffer();

		if (Util.isEmpty(Util.trim(areaDetail))) {
			return "";
		}
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLSCommomPage.class);
		JSONArray jsons = JSONArray.fromObject(areaDetail);
		for (int i = 0, total = jsons.size(); i < total; i++) {
			JSONObject data = JSONObject.fromObject(jsons.get(i));
			if (data == null || "null".equals(Util.trim(data))) {
				continue;
			}
			String target = Util.trim(data.get("target"));
			String landNo = Util.trim(data.get("landNo"));
			String ttlNum = Util.trim(data.get("ttlNum"));
			String ttlBal = Util.trim(data.get("ttlBal"));

			temp.append("<tr>");
			temp.append("<td>").append(target).append("</td>");
			temp.append("<td>").append(landNo).append("</td>");
			temp.append("<td>").append(ttlNum).append("</td>");
			temp.append("<td>").append(NumConverter.addComma(ttlBal))
					.append("</td>");
			temp.append("</tr>");
		}
		if (temp.length() > 0) {
			StringBuffer berforHtml = new StringBuffer();
			// commonPage.ms03=土地地址
			// commonPage.ms04=地號
			// commonPage.ms05=已敘做總戶數
			// commonPage.ms06=已敘做總金額
			berforHtml.append("<table class='tb2' style='width:100%'>");
			berforHtml.append("<tr>");
			berforHtml.append("<th style='width:50%'>")
					.append(prop.getProperty("commonPage.ms03"))
					.append("</th>");
			berforHtml.append("<th style='width:15%'>")
					.append(prop.getProperty("commonPage.ms04"))
					.append("</th>");
			berforHtml.append("<th style='width:15%'>")
					.append(prop.getProperty("commonPage.ms05"))
					.append("</th>");
			berforHtml.append("<th style='width:20%'>")
					.append(prop.getProperty("commonPage.ms06"))
					.append("</th>");
			berforHtml.append("</tr>");
			temp.insert(0, berforHtml);
			temp.append("</table >");
		}
		return temp.toString();
	}

	/**
	 * 償還方式組字
	 * 
	 * @param l140s02E
	 *            償還方式
	 * @param codeMap
	 *            代碼對應表
	 * 
	 *            <pre>
	 *            L140S02E_payWay :償還方式種類
	 * </pre>
	 * 
	 * @return
	 */
	public static String toWordByL140S02E(L140S02E l140s02e,
			Map<String, CapAjaxFormResult> codeMap) {
		String payWay = Util.trim(l140s02e.getPayWay());
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1151S01Page.class);
		BigDecimal payWayAmt = l140s02e.getPayWayAmt();
		String payWayOth = l140s02e.getPayWayOth();
		StringBuffer temp = new StringBuffer();
		if (Util.isNotEmpty(payWay)) {
			String payWayStr = Util.trim(codeMap.get("L140S02E_payWay").get(
					payWay));
			if ("3".equals(payWay) || "4".equals(payWay)) {
				temp.append(payWayStr.replace("：",
						"：" + NumConverter.addComma(payWayAmt)));
			} else if ("6".equals(payWay)) {
				temp.append(payWayOth);
			} else if ("7".equals(payWay)) {
				// L140S02E.payWay7=按月付息，契約到期清償本金。
				temp.append(prop.getProperty("L140S02E.payWay7"));
			} else {
				temp.append(payWayStr);
			}
		}

		// //前次寬限期起始年月
		String lastYM = Util.trim(l140s02e.getLastYM());
		// //前次寬限期(起
		String extFrom = Util.trim(l140s02e.getExtFrom());
		// //前次寬限期(迄
		String extEnd = Util.trim(l140s02e.getExtEnd());
		// //截至前次剩餘之寬限期
		String overTerm = Util.trim(l140s02e.getOverTerm());
		if (Util.isNotEmpty(lastYM)) {
			// L140S02E.lastYM=前次寬限期起始年月
			temp.append(prop.getProperty("L140S02E.lastYM")).append("：")
					.append(lastYM).append("，");
			// L140S02E.extFrom=前次寬限期(起)
			temp.append(prop.getProperty("L140S02E.extFrom")).append("：")
					.append(Util.addZeroWithValue(extFrom, 3)).append("，");
			// L140S02E.extEnd=前次寬限期(迄)
			temp.append(prop.getProperty("L140S02E.extEnd")).append("：")
					.append(Util.addZeroWithValue(extEnd, 3)).append("，");
			// L140S02E.overTerm=截至前次剩餘之寬限期
			temp.append(prop.getProperty("L140S02E.overTerm")).append("：")
					.append(overTerm).append("。");
		}

		String nowExtend = l140s02e.getNowExtend();
		String nowFrom = Util.addZeroWithValue(
				Util.trim(l140s02e.getNowFrom()), 3);
		String nowEnd = Util.addZeroWithValue(Util.trim(l140s02e.getNowEnd()),
				3);
		String nowTerm = Util.addZeroWithValue(
				Util.trim(l140s02e.getNowTerm()), 3);
		if ("Y".equals(nowExtend)) {
			// 寬限期字串
			String nowExtendStr = "";
			if (Util.isNotEmpty(nowFrom) && Util.isNotEmpty(nowEnd)) {
				// L140S02E.nowFrom=寬限期
				// page5.054=期
				nowExtendStr = prop.getProperty("L140S02E.nowFrom") + nowFrom
						+ prop.getProperty("page5.054") + " － " + nowEnd
						+ prop.getProperty("page5.054");
				// page5.078=按月付息，餘
				nowExtendStr += "，" + prop.getProperty("page5.078") + nowTerm
						+ prop.getProperty("page5.054");
				if ("1".equals(payWay)) {
					// page5.083=本息平均攤還。
					nowExtendStr += prop.getProperty("page5.083");
				}

				if ("3".equals(payWay)) {
					// page5.079=本金平均攤還。
					nowExtendStr += prop.getProperty("page5.079");
				}
				temp.append(nowExtendStr);
			}
		}
		if(true){
			String adjCheck = l140s02e.getAdjCheck();
			if ("Y".equals(adjCheck)) {
				String adjKind = Util.trim(l140s02e.getAdjKind());
				String adjKind_s = Util.trim(l140s02e.getAdjKind_s());
				String adjDisasTp = Util.trim(l140s02e.getAdjDisasTp());
				String adjItem = Util.trim(l140s02e.getAdjItem());

				if (Util.isNotEmpty(adjKind)) {
					temp.append("<br/>").append(
							codeMap.get("L140S02E_adjKind").get(adjKind));
					
					if (Util.isNotEmpty(adjKind_s) && codeMap.containsKey("L140S02E_adjKind_s")) {
						temp.append(" - ").append(codeMap.get("L140S02E_adjKind_s").get(adjKind_s));
					}
					if (Util.isNotEmpty(adjDisasTp) && codeMap.containsKey("L140S02E_adjDisasTp")) {
						temp.append("：").append(codeMap.get("L140S02E_adjDisasTp").get(adjDisasTp));
					}
				}
				if (Util.isNotEmpty(adjItem)) {
					temp.append("<br/>").append(
							codeMap.get("L140S02E_adjItem").get(adjItem));
				}

				String adjCapSNum = Util.addZeroWithValue(
						Util.trim(l140s02e.getAdjCapSNum()), 3);
				String adjCapENum = Util.addZeroWithValue(
						Util.trim(l140s02e.getAdjCapENum()), 3);
				String adjIntSNum = Util.addZeroWithValue(
						Util.trim(l140s02e.getAdjIntSNum()), 3);
				String adjIntENum = Util.addZeroWithValue(
						Util.trim(l140s02e.getAdjIntENum()), 3);
				String amorENum = Util.addZeroWithValue(
						Util.trim(l140s02e.getAmorENum()), 3);

				if (Util.isNotEmpty(adjCapSNum)) {
					// L140S02E.adjCapSNum=本金展延起始期
					temp.append("<Br/>")
							.append(prop.getProperty("L140S02E.adjCapSNum"))
							.append("：").append(adjCapSNum);
				}
				if (Util.isNotEmpty(adjCapENum)) {
					// L140S02E.adjCapENum=本金展延截止期
					temp.append("<Br/>")
							.append(prop.getProperty("L140S02E.adjCapENum"))
							.append("：").append(adjCapENum);
				}

				if ("2".equals(adjItem)) {
					if (Util.isNotEmpty(adjIntSNum)) {
						// L140S02E.adjIntSNum=利息展延起始期
						temp.append("<Br/>")
								.append(prop.getProperty("L140S02E.adjIntSNum"))
								.append("：").append(adjIntSNum);
					}
					if (Util.isNotEmpty(adjIntENum)) {
						// L140S02E.adjIntENum=利息展延截止期
						temp.append("<Br/>")
								.append(prop.getProperty("L140S02E.adjIntENum"))
								.append("：").append(adjIntENum);
					}
					if (Util.isNotEmpty(amorENum)) {
						// prop.getProperty("L140S02E.amorSNum")=應收利息攤還截止期
						temp.append("<Br/>")
								.append(prop.getProperty("L140S02E.amorENum"))
								.append("：").append(amorENum);
					}
				}

			}
		}
		return temp.toString();
	}

	public static BigDecimal calc_L140S02D_nowRate(L140S02D l140s02d){
		String pmFlag = Util.trim(l140s02d.getPmFlag());
		BigDecimal pm_rate = (l140s02d.getPmRate()!=null)?l140s02d.getPmRate():BigDecimal.ZERO;			
		BigDecimal baseRate = l140s02d.getBaseRate();
		if(baseRate==null){
			//可能該 利率代碼 已停用
			return null;
		}else{
			BigDecimal totalRate = "P".equals(pmFlag) ? baseRate.add(pm_rate) : baseRate.subtract(pm_rate);
			return totalRate;
		}
	}
	
	/**
	 * 利率組字
	 * 
	 * @param l140s02d
	 *            利率明細
	 * @param rateMap
	 *            利率基礎
	 * @param codeMap
	 *            代碼對應表
	 * 
	 *            <pre>
	 *            L140S02C_intWay:計息方式
	 *            L140S02C_rIntWay:收息方式
	 *            L140S02C_decFlag:省息遞減
	 *            L140S02D_pmFlag:加減利率
	 *            lms1405s0204_rateKind:利率方式
	 *            L140S02D_rateChgWay:利率變動方式
	 *            L140S02D_rateChgWay2:利率變動方式周期
	 * </pre>
	 * @param rateUserType
	 *            自訂利率
	 * 
	 * @param rateBy070
	 *            取得不存在於MISLNRAT的利率名稱
	 * @return
	 */
	public static String toWordByL140S02D(L140S02D l140s02d,
			LinkedHashMap<String, String> rateMap,
			Map<String, CapAjaxFormResult> codeMap,
			LinkedHashMap<String, String> rateUserTypeMap, L140S02C l140s02c,
			HashMap<String, String> rateBy070) {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1151S01Page.class);
		StringBuffer result = new StringBuffer();
		if (!UtilConstants.DEFAULT.是.equals(l140s02d.getIsUseBox())) {
			return "";
		}
		String taxRateStr = "";
		BigDecimal taxRate = Util.parseBigDecimal(l140s02c.getTaxRate());
//		if ("1".equals(l140s02c.getIntWay())) {
			if (BigDecimal.ZERO.compareTo(taxRate) != 0
					&& BigDecimal.ONE.compareTo(taxRate) != 0) {
				// L140M01A.msg067=除以
				taxRateStr = prop.getProperty("L140M01A.msg067")
						+ taxRate.toString();
			}
//		}

		// page5.065=第
		String 第 = prop.getProperty("page5.065");
		// page5.054=期
		String 期 = prop.getProperty("page5.054");
		String rateType = Util.trim(l140s02d.getRateType());
		String pmFlag = Util.trim(l140s02d.getPmFlag());
		// 第1期～第12期按行員消貸利率1.62%，機動利率；
		if (Util.isNotEmpty(l140s02d.getBgnNum()) && l140s02d.getBgnNum() != 0) {

			result.append(第).append(l140s02d.getBgnNum()).append(期);
			result.append("~");
			result.append(第).append(l140s02d.getEndNum()).append(期);
		}

		String rateUserTypeStr = "";
		if (CrsUtil.RATE_TYPE_01.equals(rateType)) {
			result.append(rateMap.get(rateType));
			result.append("【")
					.append(NumConverter.addComma(l140s02d.getRateUser()))
					.append("％").append("】");
			result.append(taxRateStr);
			String rateUserType = Util.trim(l140s02d.getRateUserType());
			// L140S02D.rateUserType=自訂利率參考指標
			if (Util.isNotEmpty(rateUserType)) {
				rateUserTypeStr = StrUtils.concat("，",
						prop.getProperty("L140S02D.rateUserType"), " ",
						rateUserType, " ",
						Util.trim(rateUserTypeMap.get(rateUserType)));
			}

		} else {
			if (rateMap.containsKey(rateType)) {
				/*
				2015.10.12授管處王惜齡襄理 接到分行反應
				　　在 7D 時，印出的說明文字只有 6R 的代碼，但無7D的代碼
				　　容易使人誤解
				
				7D-新兆豐金控集團職員貸款利率（６Ｒ）
				
				在特定利率代碼(EX：7D）時，一併印出代碼
				*/
				String[] decoArr = {"7D"};
				String rateDesc = rateMap.get(rateType);
				if(CrsUtil.inCollection(rateType, decoArr)){
					rateDesc = (rateType+"-")+rateDesc;
				}				
				result.append(rateDesc);

			} else {
				// 當利率不存在於MISLNRAT
				if (Util.isNotEmpty(rateType) && rateBy070 != null
						&& rateBy070.containsKey("00" + rateType)) {
					result.append(rateBy070.get("00" + rateType));
				}
			}

			if (Util.isNotEmpty(pmFlag)) {
				result.append(codeMap.get("L140S02D_pmFlag").get(pmFlag));
				result.append(
						NumConverter.addComma(NumConverter.addComma(Util
								.parseBigDecimal(l140s02d.getPmRate()))))
						.append("％");
			} else {
				// if (l140s02d.getBaseRate() != null) {
				// result.append(Util.parseBigDecimal(l140s02d.getBaseRate()))
				// .append("％");
				// }
			}

			result.append(taxRateStr);
			BigDecimal nowRate = l140s02d.getNowRate();
			if (nowRate != null) {
				// page5.071=目前為
				result.append("(").append(prop.getProperty("page5.071"));
				result.append("【").append(NumConverter.addComma(nowRate))
						.append("％").append("】");
				result.append(")");
			}

		}
		// 第13期～第24期月變動消費金融指標利率加年率2%(目前為【3.37%】)，定期浮動，每月調整乙次；
		String rateFlag = Util.trim(l140s02d.getRateFlag());
		if (Util.isNotEmpty(rateFlag)) {
			result.append("，");
			result.append(codeMap.get("lms1405s0204_rateKind").get(rateFlag));
			if ("3".equals(rateFlag)) {
				String rateChgWay = Util.trim(l140s02d.getRateChgWay());
				if ("1".equals(rateChgWay)) {
					// page5.072=每
					String rateChgWay2 = Util.trim(l140s02d.getRateChgWay2());

					result.append(prop.getProperty("page5.072"));
					result.append(codeMap.get("L140S02D_rateChgWay2").get(
							rateChgWay2));

					// page5.073=調整一次
					result.append(prop.getProperty("page5.073"));
				} else {
					result.append(codeMap.get("L140S02D_rateChgWay").get(
							rateChgWay));
				}
			}
		}
		result.append(rateUserTypeStr);
		return result.toString();
	}

	public static Class<?> changeClass(Class<?> clazz) { 
		/*try {
			if (ArrayUtils.indexOf(LMSUtil.C120Class, clazz) != -1) {
				String cn = clazz.getSimpleName().replace("120", "101");
				return Class.forName("com.mega.eloan.lms.model." + cn);
			}
		} catch (ClassNotFoundException e) {
			logger.error("changeClass:", e);
		}*/
		/*
	  		原本寫法 Class.forName("com.mega...") 
	  	 	在 checkmarx 會被認為是 中風險 Download_of_Code_Without_Integrity_Check
		*/
		if (ArrayUtils.indexOf(LMSUtil.C120Class, clazz) != -1) {
			String cn = clazz.getSimpleName().replace("120", "101");
			
			Map<String, Class<?>> map_class = new HashMap<String, Class<?>>();
			map_class.put("C101M01A", C101M01A.class);
			map_class.put("C101S01A", C101S01A.class);
			map_class.put("C101S01B", C101S01B.class);
			map_class.put("C101S01C", C101S01C.class);
			map_class.put("C101S01D", C101S01D.class);
			map_class.put("C101S01E", C101S01E.class);
			map_class.put("C101S01F", C101S01F.class);
			map_class.put("C101S01G", C101S01G.class);
			map_class.put("C101S01H", C101S01H.class);
			map_class.put("C101S01I", C101S01I.class);
			map_class.put("C101S01J", C101S01J.class);
			map_class.put("C101S01K", C101S01K.class);
			map_class.put("C101S01L", C101S01L.class);
			map_class.put("C101S01M", C101S01M.class);
			map_class.put("C101S01N", C101S01N.class);
			map_class.put("C101S01O", C101S01O.class);
			map_class.put("C101S01P", C101S01P.class);
			map_class.put("C101S01Q", C101S01Q.class);
			map_class.put("C101S01R", C101S01R.class);
			map_class.put("C101S01S", C101S01S.class);
			map_class.put("C101S01U", C101S01U.class);
			map_class.put("C101S01V", C101S01V.class);
			map_class.put("C101S01W", C101S01W.class);
			map_class.put("C101S01X", C101S01X.class);
			map_class.put("C101S01Y", C101S01Y.class);
			map_class.put("C101S01Z", C101S01Z.class);
			map_class.put("C101S02A", C101S02A.class);
			map_class.put("C101S02B", C101S02B.class);
			if(map_class.containsKey(cn)){
				return map_class.get(cn);
			}
		}
		return clazz;
	}

	@SuppressWarnings("rawtypes")
	public static GenericBean changeModel(Class<?> clazz, GenericBean model) {
		if(model!=null){
			try {
				if (ArrayUtils.indexOf(LMSUtil.C120Class, clazz) != -1) {
					Class<?> clazz101 = changeClass(clazz);
					Constructor constructor = clazz101
							.getConstructor(new Class<?>[] {});
					Object obj = constructor.newInstance(new Object[] {});
					GenericBean bean = (GenericBean) obj;

					DataParse.copy(model, bean);
					return bean;
				}
			} catch (Exception e) {
				logger.error("changeModel:", e);
			}
		}
		return model;
	}

	public static List<? extends GenericBean> changeModelList(Class<?> clazz, List<? extends GenericBean> models) {
		List<GenericBean> lists = new ArrayList<GenericBean>();
		if (models != null) {
			try {
				for (GenericBean model : models) {
					if (ArrayUtils.indexOf(LMSUtil.C120Class, clazz) != -1) {
						Class<?> clazz101 = changeClass(clazz);
						Constructor constructor = clazz101.getConstructor(new Class<?>[] {});
						Object obj = constructor.newInstance(new Object[] {});
						GenericBean bean = (GenericBean) obj;

						DataParse.copy(model, bean);
						lists.add(bean);
					}
				}

			} catch (Exception e) {
				logger.error("changeModel:", e);
			}
		}
		return lists;
	}

	
	/**
	 * 依 C101S01G、C101S01Q(C120S01G、C120S01Q) 的quote欄位
	 * 來判斷是否在 UI 顯示值
	 * 
	 * EX:在個金徵信, 對 custId 已算出房貸模型、非房貸模型 的分數
	 * 但只引用 房貸模型
	 * 則在 UI 顯示時, 雖然C101S01Q(C120S01Q)已有值
	 * 但呈現上, 顯示空白
	 * 
	 * @param quote
	 * @return
	 */
	public static boolean showMarkModelInfoByQuote(String quote){
		return Util.equals("Y", Util.trim(quote));
	}

	
	public static  boolean isQuote(C101M01A c101m01a, String markModel){
		return _isQuote(c101m01a.getMarkModel(), markModel);
	}
	public static  boolean isQuote(C120M01A c120m01a, String markModel){
		return _isQuote(c120m01a.getMarkModel(), markModel);			
	}
	private static boolean _isQuote(String modelVal, String markModel){
		HashSet<String> s = new HashSet<String>();
		for(String savedMarkModel : Util.trim(modelVal).split("|")){
			s.add(savedMarkModel);
		}		
		return s.contains(markModel);
	}	
	public static  String fetchQuoteValue(boolean quote_C101S01Q_C120S01Q){
		return quote_C101S01Q_C120S01Q?"Y":"";		
	}
	
	public static String showGradeWithRatingDesc(String markModel, String raw_grade){
		String grade = Util.trim(raw_grade);
		
		if(Util.equals(UtilConstants.L140S02AModelKind.房貸, markModel)){
			//J-111-0221 僅顯示評等1~10等，無須再轉換
			return grade;// + "("+ LMSUtil.getFinalGrade(markModel, grade) + ")";	
		}else if(Util.equals(UtilConstants.L140S02AModelKind.非房貸, markModel)){
			return grade;//不像房貸 1~3:特A, 4~5:A
		}else if(Util.equals(UtilConstants.L140S02AModelKind.卡友貸, markModel)){
			return grade;
		}
		return grade;
	}

	public static String getC900M01EMsg(String custName, C101S01J o){
		
		String sourceNo = Util.trim(o.getRSourceNo());
		String comId = Util.trim(o.getRComId());
		String comName = Util.trim(o.getRComName());
		String comTarget = Util.trim(o.getRComTarget());
		//---
		String prefix = "";
		if(Util.equals("0", o.getRDataSrc())){//卡務中心
			prefix = "該戶 "+custName+" 經比對符合卡務中心提供(通報文號："+sourceNo+")之疑似";
		}else if(Util.equals("9", o.getRDataSrc())){
			prefix = "該戶 "+custName+" 經比對符合通報文號"+sourceNo+"中，疑似客戶";
		}else{
			prefix = "該戶 "+custName+" 疑似";
		}
		
		return prefix
				+"利用偽造證件或財力證明，用以申辦信用卡或貸款，請審慎評估辦理。"
				+"【通報內容:"
				+"任職公司之名稱("+comName+")、統一編號("+comId+")及地址("+comTarget+")"
				+"】";
	}
	
	public static void clearL140S02A_grade(L140S02A model){
		model.setCustId("");
		model.setDupNo("");
		model.setGrade1("");		
	}
	
	public static void clearL140S02F_house_item(L140S02F l140s02f){
		clearL140S02F_house_item(l140s02f, false);
	}
	public static void clearL140S02F_house_item(L140S02F l140s02f, boolean force_clear){
		if(l140s02f==null){
			return;
		}
		boolean do_clear = false;
		if (Util.equals("20", l140s02f.getRatePlan())
				// J-113-0227 配合房貸核貸成數新增檢核邏輯，
				// 當[房貸利率方案]=06-青年安加購屋優惠貸款新增欄位[是否符合自住型房貸成長方案之8成條件]
				// 若[是否符合自住型房貸成長方案之8成條件]=Y，則開放填寫[自住型房貸成長方案成案條件]
				|| (Util.equals("06", l140s02f.getRatePlan()) 
						&& Util.equals("Y", l140s02f.getIsHousePlanEightyPer())) ) {
			// ok
		}else{
			do_clear = true;	
		}
		
		if(do_clear || force_clear){
			l140s02f.setHouse_item1("");
			l140s02f.setHouse_item2("");
			l140s02f.setHouse_item3("");
			l140s02f.setHouse_item4("");
			l140s02f.setHouse_item5("");
			l140s02f.setHouse_item6("");
			l140s02f.setHouse_item7("");
		}
	}
	
	public static void chkYN_N(L140S02A model){
		model.setChkYN(UtilConstants.DEFAULT.否);
	}
	
	public static HashMap<String, String> procMarkModel_0(C101S01G model, String bailout_flag, String checkItemRange) {
		HashMap<String, String> r = new HashMap<String, String>();
		r.put("alertMsg_markModel_0", Util.trim(LMSUtil.getGradeMessage(model,
				UtilConstants.L140S02AModelKind.房貸, OverSeaUtil.TYPE_UI, bailout_flag, checkItemRange))); // 票交所與聯徵特殊負面資訊
		return r;
	}
	
	public static HashMap<String, String> procMarkModel_G(C101S01G model, String bailout_flag, String checkItemRange) {
		HashMap<String, String> r = new HashMap<String, String>();
		r.put("grade1_markModel_1", Util.trim(model.getGrade1())); // 初始評等
		r.put("grade2_markModel_1", Util.trim(model.getGrade2())); // 調整評等
		r.put("grade2Status_markModel_1", ClsUtil.getAdjustStatus(model)); // 調整評等
		r.put("grade3_markModel_1", Util.trim(model.getGrade3())); // 最終評等
		r.put("alertMsg_markModel_1", Util.trim(LMSUtil.getGradeMessage(model,
				UtilConstants.L140S02AModelKind.房貸, OverSeaUtil.TYPE_UI, bailout_flag, checkItemRange))); // 票交所與聯徵特殊負面資訊
		r.put("varVer_markModel_1", Util.trim(model.getVarVer()));
		return r;
	}
	
	public static HashMap<String, String> procMarkModel_G_N(C101S01G_N model) {
		HashMap<String, String> r = new HashMap<String, String>();
		r.put("grade1_markModel_1_N", Util.trim(model.getGrade1())); // 初始評等
		r.put("grade3_markModel_1_N", Util.trim(model.getGrade3())); // 最終評等
		r.put("varVer_markModel_1_N", Util.trim(model.getVarVer()));
		return r;
	}
	

	public static HashMap<String, String> procMarkModel_Q(C101S01Q model, String bailout_flag, String checkItemRange) 
	throws CapException {
		HashMap<String, String> r = new HashMap<String, String>();
		r.put("grade1_markModel_2", Util.trim(model.getGrade1())); // 初始評等
		r.put("grade2_markModel_2", Util.trim(model.getGrade2())); // 調整評等
		r.put("grade2Status_markModel_2", getAdjustStatus(model)); // 調整評等
		r.put("grade3_markModel_2", Util.trim(model.getGrade3())); // 最終評等
		r.put("alertMsg_markModel_2", Util.trim(LMSUtil.getGradeMessage(model,
				UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_UI, bailout_flag, checkItemRange))); // 票交所與聯徵特殊負面資訊
		r.put("varVer_markModel_2", Util.trim(model.getVarVer()));
		return r;
	}
	
	public static HashMap<String, String> procMarkModel_Q_N(C101S01Q_N model) {
		HashMap<String, String> r = new HashMap<String, String>();
		r.put("grade1_markModel_2_N", Util.trim(model.getGrade1())); // 初始評等
		r.put("grade3_markModel_2_N", Util.trim(model.getGrade3())); // 最終評等
		r.put("varVer_markModel_2_N", Util.trim(model.getVarVer()));
		return r;
	}
	
	public static HashMap<String, String> procMarkModel_R(C101S01R model, String bailout_flag, String checkItemRange) 
	throws CapException {
		HashMap<String, String> r = new HashMap<String, String>();
		r.put("grade1_markModel_3", Util.trim(model.getGrade1())); // 初始評等
		r.put("grade2_markModel_3", Util.trim(model.getGrade2())); // 調整評等
		r.put("grade2Status_markModel_3", getAdjustStatus(model)); // 調整評等
		r.put("grade3_markModel_3", Util.trim(model.getGrade3())); // 最終評等
		r.put("alertMsg_markModel_3", Util.trim(LMSUtil.getGradeMessage(model,
				UtilConstants.L140S02AModelKind.卡友貸, OverSeaUtil.TYPE_UI, bailout_flag, checkItemRange))); // 票交所與聯徵特殊負面資訊
		r.put("varVer_markModel_3", Util.trim(model.getVarVer()));
		//==========
		r.put("j10_markModel_3", Util.trim(LMSUtil.getKCS003_J10(model.getJ10_score()))); // 聯徵J10
		r.put("sprtRating_markModel_3", Util.trim(model.getSprtRating())); // 支援評等
		r.put("KCS003Reason_markModel_3", LMSUtil.getKCS003Reason(model, "<br/>")); 
		r.put("isKind", Util.trim(model.getIsKind()));// 聯徵-擔保品類別
		
		return r;
	}
	
	public static HashMap<String, String> procMarkModel_R_N(C101S01R_N model) {
		HashMap<String, String> r = new HashMap<String, String>();
		r.put("grade1_markModel_3_N", Util.trim(model.getGrade1())); // 初始評等
		r.put("grade3_markModel_3_N", Util.trim(model.getGrade3())); // 最終評等
		r.put("varVer_markModel_3_N", Util.trim(model.getVarVer()));
		r.put("sprtRating_markModel_3_N", Util.trim(model.getSprtRating())); // 支援評等
		return r;
	}
	
	public static HashMap<String, String> procMarkModel_R_default(String varVer) 
	{
		HashMap<String, String> r = new HashMap<String, String>();
		String mark = "△";
		r.put("grade1_markModel_3", mark); // 初始評等
		r.put("grade2_markModel_3", ""); // 調整評等
		r.put("grade2Status_markModel_3", "無調整"); // 調整評等
		r.put("grade3_markModel_3", ""); // 最終評等
		r.put("alertMsg_markModel_3", ""); // 票交所與聯徵特殊負面資訊
		r.put("varVer_markModel_3", varVer);
		//==========
		r.put("j10_markModel_3", ""); // 聯徵J10
		r.put("sprtRating_markModel_3", ""); // 支援評等
		r.put("KCS003Reason_markModel_3", "");
		return r;
	}
	
	public static HashMap<String, String> procMarkModel_Q(C120S01Q c120s01q, String bailout_flag, String checkItemRange) 
	throws CapException {
		C101S01Q c101s01q = new C101S01Q();
		DataParse.copy(c120s01q, c101s01q);
		return ClsUtil.procMarkModel_Q(c101s01q, bailout_flag, checkItemRange);		
	}	
	
	public static HashMap<String, String> procMarkModel_R(C120S01R c120s01r, String bailout_flag, String checkItemRange) 
	throws CapException {
		C101S01R c101s01r = new C101S01R();
		DataParse.copy(c120s01r, c101s01r);
		return ClsUtil.procMarkModel_R(c101s01r, bailout_flag, checkItemRange);		
	}	
	
	/**
	 * 因 C101S01G,C101S01Q 中都有 chkItem1, chkItem2...
	 * 而在 借保人 的頁面，開啟 G、Q的評分表時
	 * 都要透過 buildItem 去將  codeType="HaveNoNa" 的 radio 組合出來
	 * 因 id 都相同，若先開啟 G，再開Q，則 Q 的 chkItem radio 都跑不出來
	 * 所以在 CLS1131S01QPanel.html 
	 * 將 chkItem1  改成  q_chkItem1
	 */
	public static void set_Q_chkItem(CapAjaxFormResult result, C101S01Q model_q) 
	throws CapException{
		String[] arr = {"chkItem1", "chkItem2", "chkItem3", "chkItem4", "chkItem5", "chkItem6", "chkItem7", "chkItem8"};
		for(String col:arr){
			String val = Util.trim(model_q.get(col));
			result.set("q_"+col, val);
		}
	}
	
	public static void set_R_chkItem(CapAjaxFormResult result, C101S01R model_r) 
	throws CapException{
		String[] arr = {"chkItem1", "chkItem2", "chkItem3", "chkItem4", "chkItem5", "chkItem6", "chkItem7", "chkItem8"};
		for(String col:arr){
			String val = Util.trim(model_r.get(col));
			result.set("r_"+col, val);
		}
	}
	
	public static void setRepayFund(L140M03A l140m03a, boolean r){
		/*
		 * 放'', 不放 null, 避免 result.set(...) 時, 未把值塞入 json 
		 * 
		 * 放'', 不放N, 讓列印時, 可不再判斷
		 */
		l140m03a.setRepayFund(r?"Y":"");		
	}
	
	public static String c120s01q_negativeInfo(C120S01Q c120s01q){
		StringBuffer negativeInfo = new StringBuffer();
		try{
			for (int i = 1; i <= 8; i++) {
				if ("1".equals(c120s01q.get("chkItem" + i))) {
					negativeInfo.append("(").append(i).append(")");
				}
			}
		}catch(Exception e){
			
		}
		return negativeInfo.toString();
	}
	
	public static String c120s01q_adjGrade(C120S01Q q){
		if(q != null){
			return (Util.equals("2", q.getAdjustStatus())?"-":"")+ Util.trim(q.getGrade2());	
		}			  
		return "";
	}
	
	public static final String GRADE_DIV_MARLMODEL_0 = "gradeDiv_markModel_0";
	public static final String GRADE_DIV_MARLMODEL_1 = "gradeDiv_markModel_1";
	public static final String GRADE_DIV_MARLMODEL_2 = "gradeDiv_markModel_2";
	public static final String GRADE_DIV_MARLMODEL_3 = "gradeDiv_markModel_3";
	
	/**
	 * 說明文字, 將 ? 填入查詢日(非資料日)
	 * 	是否持有信用卡、授信往來紀錄之資料來源為  ? 之聯徵 KRS008(持有信用卡紀錄) 及 
	 * 	BAM095(授信額度,擔保品,金額,還款紀錄資訊) 查詢結果。
	 */
	public static void set_date_jcicFlg_V_NN(CapAjaxFormResult result, Date d){
		String val = "";
		val = Util.trim(TWNDate.toAD(d));
		result.set("date_jcicFlg_V_NN", val);
	}
	public static void set_date_jcicFlg_V_NN(JSONObject jsonObj, Date d){
		String val = "";
		val = Util.trim(TWNDate.toAD(d));
		jsonObj.put("date_jcicFlg_V_NN", val);
	}
	public static void set_ntCode(JSONObject jsonObj, String ntCode){
		jsonObj.put("ntCode", ntCode);
	}
	public static void set_msg_Laa(CapAjaxFormResult formResult, String msg){		
		formResult.set("msg_Laa", msg);
	}
	public static void set_msg_L120S09B(CapAjaxFormResult formResult, L120S09B l120s09b
			, L120S09A l120s09a, Map<String, String> map_ncResult){
		String memo = "";
		String queryDateS = "";
		String ncResult = "";
		String refNo = "";
		String uniqueKey = "";
		String ncCaseId = "";
		String cmfwarnpResult ="";
		String cmfwarnpQueryResultInfo = "";
		Properties prop_cls1131 = MessageBundleScriptCreator
				.getComponentResource(CLS1131S01Panel.class);
		if(l120s09a!=null){
			memo = Util.trim(l120s09a.getMemo());
			cmfwarnpResult = Util.trim(l120s09a.getCmfwarnpResult());
			if (Util.equals(cmfwarnpResult,"1") &&Util.isNotEmpty(l120s09a.getCmfwarnpQueryResultInfo())) {
				cmfwarnpQueryResultInfo = MessageFormat.format(prop_cls1131.getProperty("cmfwarnpQueryResultInfo")
						, l120s09a.getCustId()
						, l120s09a.getCustName()
						, Util.trim(l120s09a.getCmfwarnpQueryResultInfo())
				);
				//cmfwarnpQueryResultInfo = "借款人ID "+ l120s09a.getCustId() +" "+ l120s09a.getCustName() +"於"+ Util.trim(l120s09a.getCmfwarnpQueryResultInfo()) +"被列為「洗錢防制法第十五條之二第六項帳戶帳號暫停限制功能或逕予關閉管理辦法」所稱「經裁處告誡者」，請於高風險欄位或綜合評估頁籤補充說明敘做理由";
			}
			else if(Util.equals(cmfwarnpResult, "3")){
				cmfwarnpQueryResultInfo = prop_cls1131.getProperty("cmfwarnpQueryResultInfo.err");
			}
		}
		
		if(l120s09b!=null){
			queryDateS = Util.trim(TWNDate.toAD(l120s09b.getQueryDateS()));
			ncResult = Util.trim(l120s09b.getNcResult());
			refNo = Util.trim(l120s09b.getRefNo());
			uniqueKey = Util.trim(l120s09b.getUniqueKey());
			ncCaseId = Util.trim(l120s09b.getNcCaseId());
		}
		if(Util.isNotEmpty(ncResult) && map_ncResult.containsKey(ncResult)){
			ncResult = (ncResult+"-"+map_ncResult.get(ncResult));
		}
		formResult.set("l120s09a_memo", memo);
		formResult.set("l120s09b_queryDateS", queryDateS);
		formResult.set("l120s09b_ncResult", ncResult);
		formResult.set("l120s09b_refNo", refNo);
		formResult.set("l120s09b_uniqueKey", uniqueKey);
		formResult.set("l120s09b_ncCaseId", ncCaseId);
		formResult.set("cmfWarnpResult", cmfwarnpResult);
		formResult.set("cmfwarnpQueryResultInfo", cmfwarnpQueryResultInfo);
	}
	//========================
	@Deprecated
	public static String msg_Laa_html(C101S01J model, Properties prop_LMSCommomPage){
		String laaCtlFlag = (model==null?"":model.getLaaCtlFlag()); //應改抓 S01Y
		return inner_msg_Laa(laaCtlFlag, prop_LMSCommomPage, true);
	}
	@Deprecated
	public static String msg_Laa_txt(C101S01J model, Properties prop_LMSCommomPage){
		String laaCtlFlag = (model==null?"":model.getLaaCtlFlag()); //應改抓 S01Y
		return inner_msg_Laa(laaCtlFlag, prop_LMSCommomPage, false);
	}
	@Deprecated
	public static String msg_Laa_html(C120S01J model, Properties prop_LMSCommomPage){		
		String laaCtlFlag = (model==null?"":model.getLaaCtlFlag()); //應改抓 S01Y
		return inner_msg_Laa(laaCtlFlag, prop_LMSCommomPage, true);
	}
	private static String inner_msg_Laa(String laaCtlFlag, Properties prop_LMSCommomPage, boolean htmlFmt){		
		if( Util.isNotEmpty(Util.trim(laaCtlFlag))){
			if(Util.equals(laaCtlFlag, "0")){
				//比對後無match資料
			}else if(Util.equals(laaCtlFlag, "1")
					|| Util.equals(laaCtlFlag, "4")){
				//嚴重
				return MessageFormat.format(prop_LMSCommomPage.getProperty(htmlFmt?"clsL120M01A.error067.html":"clsL120M01A.error067"), "");
			}else if(Util.equals(laaCtlFlag, "2")
					|| Util.equals(laaCtlFlag, "3")){
				//輕微
				return MessageFormat.format(prop_LMSCommomPage.getProperty(htmlFmt?"clsL120M01A.error068.html":"clsL120M01A.error068"), "");
			}	
		}
		return "";
	}
//	public static boolean hasLaaData(C101S01E c101s01e){
//		return hasLaaData(c101s01e.getLaaYear(), c101s01e.getLaaWord(), c101s01e.getLaaNo());
//	}
//	public static boolean hasLaaData(String laaYear, String laaWord, String laaNo){
//		if(Util.isNotEmpty(Util.trim(laaYear))
//				&& Util.isNotEmpty(Util.trim(laaWord))
//				&& Util.isNotEmpty(Util.trim(laaNo))){
//			return true;	
//		}
//		return false;
//	}
	//========================
	public static void set_msg_agentPIdCmp(CapAjaxFormResult formResult, String msg){		
		formResult.set("msg_agentPIdCmp", msg);
	}
	public static String msg_agentPIdCmp(String c101s01e_agentPId, C900M01J c900m01j, String c900m01j_output_memo, Properties prop_CLS1131S01Panel){
		if(Util.isNotEmpty(c101s01e_agentPId)){
			String cmpStr = "";
			if(c900m01j==null){
				cmpStr = prop_CLS1131S01Panel.getProperty("rdo.nohave");
			}else{
				String c900m01j_category = Util.trim(c900m01j.getCategory());
				if(Util.equals("P", c900m01j_category)){
					cmpStr += prop_CLS1131S01Panel.getProperty("C900M01J.category.P");
				}else if(Util.equals("B", c900m01j_category)){
					cmpStr += prop_CLS1131S01Panel.getProperty("C900M01J.category.B");
				}else if(Util.equals("I", c900m01j_category)){
					cmpStr += prop_CLS1131S01Panel.getProperty("C900M01J.category.I");
				}else{
					cmpStr += c900m01j_category;	
				}											 
				cmpStr += "。";
				//====================
				if(Util.isNotEmpty(c900m01j_output_memo)){
					cmpStr += c900m01j_output_memo;
					if(!c900m01j_output_memo.endsWith("。")){
						cmpStr += "。";
					}	
				}
			}
			return prop_CLS1131S01Panel.getProperty("C101S01E.agentPId")+"："+c101s01e_agentPId+"。"
				+prop_CLS1131S01Panel.getProperty("label.cmp_C900M01J")+"："+cmpStr;										
			
		}
		return "";
	}
	//========================
	public static boolean is_403_603(String lnap){
		if(Util.equals("13500100", lnap)){
			return true;	
		}
		if(Util.equals("14501000", lnap)){
			return true;	
		}
		return false;
	}

	public static final String PROJ_CLASS_05 = "05"; // 新創產業 

	// J-108-0142 公教員工房貸搭配信貸專案
	// SELECT * FROM com.bcodetype where codetype='L140M01A_PROJCLASS_CLS' 包含
	//     08 特定金錢信託受益權自行設質擔保授信
	//     X1 公教人員房貸
	//     Y1 公教人員信貸

	//public static final String PROJ_CLASS_X1 = "X1"; // 公教人員房貸  
	//public static final String PROJ_CLASS_Y1 = "Y1"; // 公教人員信貸  
	
	/**
	 * 批覆時，營運中心減額
	 */
	public static BigDecimal decide_loanAmt(L140M01A l140m01a, L140S02A l140s02a ){
		if(l140m01a!=null && l140m01a.getCurrentApplyAmt()!=null 
				&& l140s02a.getLoanAmt()!=null 
				&& Util.equals(l140m01a.getCurrentApplyCurr(), "TWD") 
				&& l140m01a.getCurrentApplyAmt().compareTo(l140s02a.getLoanAmt())<0){
			return l140m01a.getCurrentApplyAmt();	
		}
		return l140s02a.getLoanAmt();
	}
	
	public static int prod_totalPeriod(Integer lnYear, Integer lnMonth){
		
		int yVal = (lnYear==null?0:lnYear.intValue());
		int mVal = (lnMonth==null?0:lnMonth.intValue());  
		int totalPeriod = (yVal*12)+mVal;
		return totalPeriod; 
	}
	public static int prod_totalPeriod(L140S02A l140s02a){
		Integer lnYear = l140s02a.getLnYear();
		Integer lnMonth = l140s02a.getLnMonth();
		return prod_totalPeriod(lnYear, lnMonth);
	}
	
	public static String getC101S01E_wm_data(boolean has_wm_qDate, String wm_flag, String wm_gra_name){
		if(has_wm_qDate){
			if(Util.equals("A", wm_flag)){
				return "該客戶理財等級為"+wm_gra_name;
			}else{
				return "無";
			}
		}
		return "";
	}
	public static void setC101S01E_wm_data(CapAjaxFormResult formResult, JSONObject model) {
		String c101s01e_wm_data = "";
		if(model!=null && Util.isNotEmpty(Util.trim(model.optString("wm_qDate")))){
			String wm_flag = Util.trim(model.optString("wm_flag"));
			String wm_gra_name = Util.trim(model.optString("wm_gra_name"));

			c101s01e_wm_data = getC101S01E_wm_data(true, wm_flag, wm_gra_name);			
		}
		formResult.set("c101s01e_wm_data", c101s01e_wm_data+(Util.isEmpty(c101s01e_wm_data)?"":"。"));
	}

	public static String to_AML_fmt_birth(Date d){
		if(CrsUtil.isNull_or_ZeroDate(d)){
			return "";	
		}else{
			return Util.trim(StringUtils.substring(Util.trim(TWNDate.toAD(d)), 0, 4));
		}		
	} 
	public static String to_AML_fmt_sex(String sex){
		if(Util.equals("M", sex) || Util.equals("F", sex)){
			return sex;
		}	
		return "";	
	} 
	
	public static void inject_SAS_AML(CapAjaxFormResult result, String ncResult
			, String refNo, String uniqueKey, String ncCaseId, String showNcResultRemark,
			String showHighRiskRemark, String ncResultRemark, String highRiskRemark){
		result.set("ncResult", ncResult);
		result.set("refNo", refNo);
		result.set("uniqueKey", uniqueKey);
		result.set("ncCaseId", ncCaseId);	
		
		// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
		// 目前for簽報書用
		result.set("showNcResultRemark", showNcResultRemark);// 動審表來的值為固定N
		result.set("showHighRiskRemark", showHighRiskRemark);// 動審表來的值為固定N	
		result.set("ncResultRemark", ncResultRemark);	// 動審表來的值為固定''
		result.set("highRiskRemark", highRiskRemark);	// 動審表來的值為固定''
	}
	public static String convert_nb_to_eloan_marry(C120S01A c120s01a){
		/*
			select * from com.bcodetype where codetype='marry' and locale='zh_TW'
			1	未婚
			2	已婚
			3	已婚(有子女)
			4	離婚
			5	殁
	
		*/
		String marry = Util.trim(c120s01a.getMarry());
		if(Util.equals(marry, "2") && Util.parseInt(c120s01a.getChild())>0){
			marry = "3";
		}
		return marry;
	}
	
	private static String _cm1_job_business_InfoStr(String cm1_job_business_code, Map<String, String> _CM1_JOB_BUSINESS_CODE_map){
		String desc_cm1_job_business = "";
		
		if(_CM1_JOB_BUSINESS_CODE_map!=null && _CM1_JOB_BUSINESS_CODE_map.containsKey(cm1_job_business_code)){
			desc_cm1_job_business = _CM1_JOB_BUSINESS_CODE_map.get(cm1_job_business_code);
		}
		return cm1_job_business_code+(Util.isNotEmpty(desc_cm1_job_business)?" ":"")+desc_cm1_job_business;
	}
	public static String getC101S01B_cm1_job_business_InfoStr(C101S01B c101s01b, Map<String, String> _CM1_JOB_BUSINESS_CODE_map){
		String cm1_job_business_code = Util.trim(c101s01b.getCm1_job_business_code());
		return _cm1_job_business_InfoStr(cm1_job_business_code, _CM1_JOB_BUSINESS_CODE_map);
	}
	public static String getC120S01B_cm1_job_business_InfoStr(C120S01B c101s01b, Map<String, String> _CM1_JOB_BUSINESS_CODE_map){
		String cm1_job_business_code = Util.trim(c101s01b.getCm1_job_business_code());
		return _cm1_job_business_InfoStr(cm1_job_business_code, _CM1_JOB_BUSINESS_CODE_map);
	}
	
	private static String _cm1_job_title_InfoStr(String cm1_title_code, String cm1_job_title, Map<String, String> _CM1_TITLE_CODE_map){				
		String desc_cm1_title = "";
		
		if(_CM1_TITLE_CODE_map!=null && _CM1_TITLE_CODE_map.containsKey(cm1_title_code)){
			desc_cm1_title = _CM1_TITLE_CODE_map.get(cm1_title_code);
			//28-其他，在BTT有開放輸入[○○○○○]
			//在0024-2 直接顯示 28 ○○○○○
			//未顯示[其他]
			if(Util.equals("28", cm1_title_code) && Util.isNotEmpty(cm1_job_title)){
				desc_cm1_title = cm1_job_title;									
			}
		}							

		return cm1_title_code+(Util.isNotEmpty(desc_cm1_title)?" ":"")+desc_cm1_title;	
	}
	public static String getC101S01B_cm1_job_title_InfoStr(C101S01B c101s01b, Map<String, String> _CM1_TITLE_CODE_map){				
		String cm1_title_code = Util.trim(c101s01b.getCm1_title_code());		
		String cm1_job_title = Util.trim(c101s01b.getCm1_job_title());
		return _cm1_job_title_InfoStr(cm1_title_code, cm1_job_title, _CM1_TITLE_CODE_map);
	}
	public static String getC120S01B_cm1_job_title_InfoStr(C120S01B c101s01b, Map<String, String> _CM1_TITLE_CODE_map){				
		String cm1_title_code = Util.trim(c101s01b.getCm1_title_code());		
		String cm1_job_title = Util.trim(c101s01b.getCm1_job_title());
		return _cm1_job_title_InfoStr(cm1_title_code, cm1_job_title, _CM1_TITLE_CODE_map);
	}
	
	public static String getPurpose(String purpose, CapAjaxFormResult purposeMap) {
		StringBuffer str = new StringBuffer();
		String[] arrayStr = purpose.split(UtilConstants.Mark.SPILT_MARK);
		for (String key : arrayStr) {
			if (Util.isEmpty(key)) {
				continue;
			}
			str.append(str.length() > 0 ? "、" : "");
			str.append(purposeMap.get(key));
		}

		return str.toString();
	}
	
	public static String getResource(String resource, CapAjaxFormResult resourceMap) {
		StringBuffer str = new StringBuffer();
		String[] arrayStr = resource.split(UtilConstants.Mark.SPILT_MARK);
		for (String key : arrayStr) {
			if (Util.isEmpty(key)) {
				continue;
			}
			str.append(str.length() > 0 ? "、" : "");
			str.append(resourceMap.get(key));
		}

		return str.toString();
	}

	public static final String REBUILDCOST_V1090101 = "2020-01-01";

	public static boolean decide_prodKind07(int cnt_prodKind07, int cnt_prodKindNot07){
		if(cnt_prodKind07>0 && cnt_prodKindNot07==0){
			return true;
		}
		return false;
	}
	
	public static boolean decide_prodKind08(int cnt_prodKind08, int cnt_prodKindNot08){
		if(cnt_prodKind08>0 && cnt_prodKindNot08==0){
			return true;
		}
		return false;
	}
	public static boolean decide_prodKind71(int cnt_prodKind71, int cnt_prodKindNot71){
		if(cnt_prodKind71>0 && cnt_prodKindNot71==0){
			return true;
		}
		return false;
	}
	public static boolean isEmptyDesc_includeHtml(String desc){
		if(Util.isEmpty(desc)
				|| Util.equals(desc, "<div></div>") 
				|| Util.equals(desc, "<p></p>") ){
			return true;
		}
		return false;
	}

	public static String build_residenceTargetDesc(C120S01A c120s01a){
		String cb_fTarget = "□";
		String cb_desc = "□";
		String str_desc = ""; 
		if(c120s01a==null){
			
		}else{
			String residenceTarget = Util.trim(c120s01a.getResidenceTarget());
			if(Util.isEmpty(residenceTarget)){
				
			}else{
				if(Util.equals(c120s01a.getResidenceTarget(), c120s01a.getFTarget())){
					cb_fTarget = "■";
				}else{
					cb_desc = "■";
					str_desc = "："+residenceTarget;
				}
			}
		}
		return cb_fTarget+"同戶籍地"+"　"+ cb_desc+"另列如下"+str_desc;
	}

	public static String get_latest_L120S15A_rptId(){
		return ClsConstants.L120S15A_rptId.V202301;
	}
	
	public static String[] get_newer_version_L120S15A_rptId(){
		return new String[]{ClsConstants.L120S15A_rptId.V202201, ClsConstants.L120S15A_rptId.V202301};
	}

	public static void calcBis_useGuarantee(L120S03A l120s03a, L140M01A l140m01a, Properties prop_CLSS07APage02, Double crdRatio, Double crdRskRatio) 
	throws CapMessageException{
		if(true){
			//信保時，模擬 input
			l120s03a.setCrdRatio(crdRatio); //B：信保保證成數%
			l120s03a.setCrdRskRatio(crdRskRatio); //B'.風險權數(未送保)%
		}
		_calcBis_inner(l120s03a, l140m01a, prop_CLSS07APage02);
	}
	
	public static void calcBis_notGuarantee(L120S03A l120s03a, L140M01A l140m01a, Properties prop_CLSS07APage02, BigDecimal collAmt, Double rskRatio) 
	throws CapMessageException{
		if(true){
			//非信保時，模擬 input
			l120s03a.setCollAmt(collAmt); //合格擔保品抵減額
			l120s03a.setRskRatio(rskRatio); //風險權數 %
		}
		_calcBis_inner(l120s03a, l140m01a, prop_CLSS07APage02);
	}
	
	private static void _calcBis_inner(L120S03A l120s03a, L140M01A l140m01a, Properties prop_CLSS07APage02)
	throws CapMessageException{
		if(true){
			//cls1141m01formhandler :: calculateBis(...)
			
			/*
			CapAjaxFormResult formResult_l120s03a = new CapAjaxFormResult(l120s03a);
			    => net.sf.json.JSONException: There is a cycle in the hierarchy
			    
				改另外的寫法    
			*/
			CapAjaxFormResult formResult_l120s03a = null;
			try{				
				formResult_l120s03a = DataParse.toResult(l120s03a); //new CapAjaxFormResult(l120s03a);
			}catch (CapException e) {
				throw new CapMessageException(e.getMessage(), null);
			}
			LMSUtil.calculateBis(formResult_l120s03a, prop_CLSS07APage02);
			//因為 額度 會有{信保 vs 非信保}的欄位, 當{非信保}時，DataParse.toBean(...) 會把{信保的 null 欄位}都填入 0
			DataParse.toBean(formResult_l120s03a.getResult(), l120s03a);				
			LMSUtil.l120s03a_clear(l120s03a);
		}
		if(true){
			//cls1141m01formhandler :: saveBis(...)
			LMSUtil.copy_column_from_L120S03A_to_L140M01A(l120s03a, l140m01a);
			/*
			驗證欄位，是否已由 L120S03A 複製到 LMS.L140M01A
				select t.crdflag, t.cntrno, tab.mainid, t.APPLYAMT, t.FCLTAMT, t.COLLAMT
					, t.RskRatio, t.Rskr1, t.Bisr1, t.Costr1
					, tab.ItemC, tab.RItemD, tab.ItemF, tab.itemG
					, t.CrdRskRatio, t.Rskr2, t.Bisr2, t.Costr2
				from lms.L120S03A t left outer join lms.l140m01a tab on t.cntrmainid=tab.mainid 
				where t.mainid in (?caseMainId)
			*/
		}
		l120s03a.setChkYN("Y");
	}

	public static String get_l120s06b_cmpDate(L120S06A l120s06a, List<L120S06B> l120s06bList){
		if(Util.isNotEmpty(Util.trim(l120s06a.getCntrNo2()))){
			Date choseDate = null;
			for(L120S06B l120s06b : l120s06bList){
				if (Util.nullToSpace(l120s06b.getCustId()).equals(
						Util.nullToSpace(l120s06a.getCustId2()))
						&& Util.nullToSpace(l120s06b.getDupNo()).equals(
								Util.nullToSpace(l120s06a.getDupNo2()))
						&& Util.nullToSpace(l120s06b.getCntrNo()).equals(
								Util.nullToSpace(l120s06a.getCntrNo2()))
						&& Util.equals(l120s06a.getRefMainId(), l120s06b.getRefMainId()) //J-111-0182 有2筆s06a[額度01-對照戶額度789],[額度02-對照戶額度789], 故意把[對照戶額度789]的欄位,在不同s06a輸入不同值
						&& "2".equals(Util.nullToSpace(l120s06b.getType()))) {
					if(choseDate==null){
						choseDate = l120s06b.getCreateTime();
					}else{
						if(choseDate!=null && LMSUtil.cmpDate(choseDate, ">", l120s06b.getCreateTime())){
							choseDate = l120s06b.getCreateTime();	
						}
					}
				}
			}
			return Util.trim(TWNDate.toAD(choseDate));
		}
		return "";			
	}

	public static void setRpt_CLS1021R01_data(Map<String, String> rptVariableMap, String rskFlag_hid_row, String c102m01a_rptId, String titleStrByRptId){
		rptVariableMap.put("C102M01A.RSKFLAG_HIDE_ROW", rskFlag_hid_row);
		rptVariableMap.put("C102M01A.RPTID", c102m01a_rptId);
		rptVariableMap.put("C102M01A.TitleStrByRptId", titleStrByRptId);
	}
	
	public static void set_FormResult_l120s06b_cmpDate(CapAjaxFormResult result, L120S06A l120s06a, L120S06B l120s06b){
		//==============
		//以下寫法, 會去比對 l120s06a 與 l120s06b 的資料
//		List<L120S06B> l120s06bList = new ArrayList<L120S06B>();
//		l120s06bList.add(l120s06b);
//		set_FormResult_l120s06b_cmpDate(result, l120s06a, l120s06bList);
		
		//==============
		//本來 l120s06a 的 對照授信戶 是甲, 在 UI 執行「引進資料」去引入 乙, 在「儲存l120s06a的對照戶」之前, l120s06a.custId2!=l120s06b.custId
		result.set("cmpDate2", Util.trim(TWNDate.toAD(CapDate.getCurrentTimestamp()))); 
	}
	
	public static void set_FormResult_l120s06b_cmpDate(CapAjaxFormResult result, L120S06A l120s06a, List<L120S06B> l120s06bList){
		result.set("cmpDate2", get_l120s06b_cmpDate( l120s06a, l120s06bList));
	} 
}
