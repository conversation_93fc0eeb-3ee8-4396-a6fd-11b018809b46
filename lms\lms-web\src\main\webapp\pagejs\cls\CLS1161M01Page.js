//alert('動用審核表[' + (viewstatus || '') + ']');
var pageAction = {
        readOnly: false,
        handler: 'cls1161formhandler',
        init: function(){
            $.ajax({
                handler: pageAction.handler,
                data: {
                    formAction: 'load'
                }
            }).done(function(response){
                pageAction.showTab4(response.useType);
                $('form').each(function(){
                    var $form = $(this);
                    var formId = $form.attr('id') || '';
                    var formData = response[formId];
                    if (formData) {
                        $form.setValue(formData);
                    }
                });
                
                // prcess page
                switch (responseJSON.page + '') {
                    case '03':
                        if (typeof panelAction != 'undefined') {
                            if ($.isFunction(panelAction.parse)) {
                                panelAction.parse(response.detials, response.useProd69Fmt);
                                if (thickboxOptions.lockDoc || _openerLockDoc) {
                                    $('#detials').lockDoc(1);
                                }
                            }
                        }
                        break;
                    default:
                        break;
                }
                ilog.debug("@CLS1161M01Page.js, mainId= "+ responseJSON.mainId);
            });
            //僅有電銷權限(EX01、EX02)時隱藏 "登錄"、"先行動用待辦控制表" 按鈕
            //根據權限隱藏特定物件
            $.ajax({
                handler: pageAction.handler,
                data: {
                    formAction: "check_only_expermission"
                }
            }).done(function(responseData){
                if(responseData.only_ex_permission){//僅有電銷權限, 無其他EL相關權限 true=是, false=否
                    $(".only-ex-permission").hide();
                }
            });
        },
        build: function(){
            // setIgnoreTempSave(true);
            $('body').buildItem();
            // 儲存
            $('#buttonPanel').find('#btnSave').on('click', function(){
                if (pageAction.check()) {
                    pageAction.save();
                }
            })        // 呈主管
            .end().find('#btnSend').on('click', function(){
                $.ajax({
                    handler: pageAction.handler,
                    data: $.extend(pageAction.getSaveData(), {
                        formAction: 'sendCheck',
                        page: responseJSON.page,
                        oid: $('#oid').val() || responseJSON.oid,
                        mainId: $('#mainId').val() || responseJSON.mainId
                    })
                }).done(function(response){
                    if (response.message) {
                        CommonAPI.confirmMessage(i18n.def["confirmApprove2"] + '<br/>' + response.message, function(b){
                            if (b) 
                                pageAction.openManager();
                        });
                    }
                    else {
                        pageAction.openManager();
                    }
                });
                
                //			if (pageAction.flowCheck()) {
                //				pageAction.openManager();
                //			}
            })        // 核准
            .end().find('#btnAccept').on('click', function(){
                pageAction.check_before_approve().done(function(){
                    
                var $form = $('#approveForm'); 
                $form.readOnlyChilds(false);
                $form.find('#approveDate').val(CommonAPI.getToday());
                $('#approveThickBox').thickbox({
                    title: i18n.def['enter'] || '',
                    width: 250,
                    height: 120,
                    readOnly: false,
                    valign: 'bottom',
                    align: 'center',
                    focus: false,
                    buttons: {
                        'sure': function(){
                            if ($form.valid()) {
                                $.thickbox.close();
                                //
                                pageAction.flow({
                                    caseType: $('#baseForm').find('#caseType').val(),
                                    activity: 'accept'
                                });
                            }
                        },
                        'cancel': function(){
                            $.thickbox.close();
                        }
                    }
                });
                });
            })        // 退回
            .end().find('#btnReturn').on('click', function(){
                pageAction.flow({
                    activity: 'back'
                });
            }).end().find('#btnReUpDate').on('click', function(){
                $.ajax({
                    handler: pageAction.handler,
                    data: $.extend({
                        oid:$('#oid').val()
                    }, {
                        formAction: 'ReUpDate'
                    })
                }).done(function(response){
                   
                });
            }).end().find('#btnPrint').on('click', function(){
                // 列印動作（當responseJSON.Auth.readOnly==false, 可能仍在編製中, 但被另一個人開啟, 未出現 saveButton ）
                if ((responseJSON && responseJSON.Auth && responseJSON.Auth.readOnly) || ( _openerLockDoc == "1") ) {
                    pageAction.Printpdf();
                }
                else {
                    // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
                    CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                        if (b) {
                            $.ajax({
                                handler: pageAction.handler,
                                data: $.extend(pageAction.getSaveData(), {
                                    formAction: 'save'
                                })
                            }).done(function(response){
                                pageAction.Printpdf();
                            });
                        }
                    });
                }
            }).end().find('#btnQueryBeforeSendMoney').on('click', function(){

                CommonAPI.confirmMessage('是否執行信貸撥款前查詢?', function(b){
                        if (b) {
                            $.ajax({
                                handler: pageAction.handler,
                                data: $.extend(pageAction.getSaveData(), {
                                    formAction: 'queryBeforeMoneyAllocating'
                                })
                            }).done(function(response){
                                
                                if(response.tipsMsg){
                                    ilog.debug(response.tipsMsg);
                                }
                            });
                        }
                    });
            });
            
            // set readOnly
            var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
            if (auth.readOnly) {
                $('body').lockDoc(true);
                thickboxOptions.lockDoc = true;
                $('#displayCntrNo').append($('#CaseType3ThickBox').children());
            }
            
            // 畫面切換table 所需設定之資料 如無設定 則直接切換
            $.extend(window.tempSave, {
                handler: pageAction.handler,
                action: 'tempSave',
                beforeCheck: function(){
                    if ($.trim($('#custInfo').html()) === '') {
                        MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.def['includeId.subTitle'] + ' ' +
                        i18n.def['val.required']);
                        return false;
                    }
                    return true;
                },
                sendData: function(){
                    return pageAction.getSaveData();
                }
            });
        },
        check: function(){
            // UPGRADETODO: 根據當前頁籤進行相應的表單驗證
            switch (responseJSON.page + '') {
                case '03':
                    // 第03頁的驗證由 panelAction 處理
                    if (typeof panelAction !== 'undefined' && $.isFunction(panelAction.check)) {
                        return panelAction.check();
                    }
                    break;
                case '04':
                    // 第04頁（先行動用）的驗證
                    var $c160m01dForm = $('#C160M01DForm');
                    if ($c160m01dForm.length > 0) {
                        return $c160m01dForm.valid();
                    }
                    break;
                default:
                    // 其他頁籤使用預設表單驗證
                    var $tabForm = $('#tabForm');
                    if ($tabForm.length > 0) {
                        return $tabForm.valid();
                    }
                    break;
            }
            return true;
        },
        /**
         * 是否顯示先行動用頁籤
         */
        showTab4: function(value){
            if (value == "Y") {
                $("#mainTab04").show();
            }
            else {
                $("#mainTab04").hide();
            }
        },
        save: function(){
            $.ajax({
                handler: pageAction.handler,
                data: $.extend(pageAction.getSaveData(), {
                    formAction: 'save'
                })
            }).done(function(response){
                if (response.useType) {
                    pageAction.showTab4(response.useType);
                }
                CommonAPI.showMessage(i18n.def["runSuccess"] || '執行成功');
            });
        },
        getSaveData: function(){
            // 根據頁面類型返回不同的數據
            switch (responseJSON.page + '') {
                case '01':
                    return {
                        C160M01AForm: JSON.stringify($('#tabForm').serializeData())  // UPGRADETODO: 修正表單ID
                    };
                case '02':
                    return {
                        C160M01AForm: JSON.stringify($('#tabForm').serializeData())  // UPGRADETODO: 修正表單ID
                    };
                case '03':
                    var saveData = {};
                    
                    // UPGRADETODO: 修正第03頁的表單資料傳送 - 使用正確的表單ID和資料格式
                    var $tabForm = $('#tabForm');
                    if ($tabForm.length > 0) {
                        // 基本表單資料
                        var formData = $tabForm.serializeData();
                        if (formData) {
                            saveData.C160M01AForm = JSON.stringify(formData);
                        }
                    }
                    
                    // UPGRADETODO: 處理第03頁的 detials 資料，確保與 CLS1161S03Panel.js 的邏輯一致
                    if (typeof panelAction !== 'undefined' && panelAction.data) {
                        saveData.detials = JSON.stringify(panelAction.data);
                    } else {
                        // 如果 panelAction.data 不存在，嘗試從其他來源取得
                        ilog.warn('panelAction.data 未定義，第03頁資料可能不完整');
                        saveData.detials = JSON.stringify({});
                    }
                    
                    return saveData;
                case '04':
                    // UPGRADETODO: 處理先行動用頁籤(第04頁)的表單資料
                    var $c160m01dForm = $('#C160M01DForm');
                    if ($c160m01dForm.length > 0) {
                        return {
                            C160M01DForm: JSON.stringify($c160m01dForm.serializeData())
                        };
                    }
                    // 如果找不到 C160M01DForm，使用預設表單
                    return {
                        C160M01AForm: JSON.stringify($('#tabForm').serializeData())
                    };
                case '05':
                    return {
                        // AML/CFT相關數據
                    };
                default:
                    return {
                        C160M01AForm: JSON.stringify($('#tabForm').serializeData())  // UPGRADETODO: 修正表單ID
                    };
            }
        },
        flowCheck: function(){
            if ($.trim($('#custInfo').html()) === '') {
                MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.def['includeId.subTitle'] + ' ' +
                i18n.def['val.required']);
                return false;
            }
            return true;
        },
        /**
         * 呈主管 & 覆核
         */
        flow: function(data){
            if (data) {
                MegaApi.confirmMessage(data.fonfirmMessage ||
                i18n.def["actoin_001"] ||
                '', function(action){
                    if (action) {
                        if (data.activity === 'accept' && data.caseType === '3') {
                            $('#progressTr').show();
                            $('#progress').html(0); // finish progress
                            pageAction.xlsToMis();
                        }
                        else {
                            pageAction.flowAction(data);
                        }
                    }
                });
            }
        },
        /**
         * 執行flow
         */
        flowAction: function(data){
            $.ajax({
                handler: pageAction.handler,
                data: $.extend(data, {
                    formAction: 'flowAction'
                })
            }).done(function(response){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                setCloseConfirm(false);
                window.close();
            });
        },
        /**
         * Excel上傳MIS DB
         */
        xlsToMis: function(data){
            $.ajax({
                handler: pageAction.handler,
                data: $.extend(data, {
                    formAction: 'xlsToMis'
                })
            }).done(function(response){
                if (response.finish) {
                    $('#progress').html(100); // finish progress
                    // lert('accept');
                    // return;
                    pageAction.flowAction({
                        activity: 'accept'
                    });
                }
                else {
                    if (/\d/.test(response.end) && /\d/.test(response.tot)) {
                        var end = parseInt(response.end);
                        var tot = parseInt(response.tot);
                        if (end <= tot && end > 0 && tot > 0) {
                            var progress = ((end / tot) * 100).toFixed(2);
                            $('#progress').html(progress); // finish progress
                            pageAction.xlsToMis({
                                start: end
                            });
                        }
                    }
                    else {
                        // alert('this is error!');
                    }
                }
            });
        },
        /**
         * 開啟主管選項
         */
        managerItem: {},
        check_before_openManager: function(){
            var my_dfd = $.Deferred(); 
            $.ajax({
                handler: pageAction.handler,
                data: {
                    formAction: "checkSend_C160M01A"
                }
            }).done(function(json){
                if(json.cfmMsg && json.cfmMsg!=""){
                    API.confirmMessage(json.cfmMsg, function(b){
                        if (b) {
                            my_dfd.resolve();
                        }
                    });
                }else{
                    my_dfd.resolve();
                }
            });
            return my_dfd.promise();
        },
        check_before_approve: function(){
            var my_dfd = $.Deferred(); 
            $.ajax({
                handler: pageAction.handler,
                data: {
                    formAction: "checkApprove_C160M01A"
                }
            }).done(function(json){
                if(json.cfmMsg && json.cfmMsg!=""){
                    API.confirmMessage(json.cfmMsg, function(b){
                        if (b) {
                            my_dfd.resolve();
                        }
                    });
                }else{
                    my_dfd.resolve();
                }
            });
            return my_dfd.promise();
        },
        openManager: function(){
            //在開啟輸入'簽章欄'的畫面前，進行檢核
            pageAction.check_before_openManager().done(function(){
                
            if ($.isEmptyObject(pageAction.managerItem)) {
                pageAction.openManagerBuild = true;
                $.ajax({
                    handler: 'flowactionformhandler',
                    data: {
                        formAction: 'manager',
                        sign: ['首長', '單位主管', '甲級主管', '乙級主管']
                    }
                }).done(function(response){
                    pageAction.managerItem = $.extend({}, response.manager);
                    var $form = $('#C160M01EForm');
                    $form.find('#manager').setItems({
                        item: pageAction.managerItem,
                        format: "{value} {key}"
                    });
                    // $form.find('#numPerson').val(1);
                });
                
                $('#C160M01EForm').find('#numPerson').change(function(){
                    $('#bossItem').empty();
                    var value = $(this).val();
                    if (value) {
                        var html = '';
                        for (var i = 1; i <= value; i++) {
                            var name = 'boss' + i;
                            html += util.addZeroBefore(i + '', 2) + '.' // +i18n.cls1161m01['manager.L3']
                            // || '授信主管'
                            html += '<select id="' + name + '" name="' +
                            name +
                            '" class="required" CommonManager="kind:2;type:2" />';
                            html += '<br/>';
                        }
                        $('#bossItem').append(html).find('select').each(function(){
                            $(this).setItems({
                                item: pageAction.managerItem,
                                format: "{value} {key}"
                            });
                        });
                    }
                });
            }
            
            $('#C160M01EThickBox').thickbox({
                title: i18n.def['err.chooseBoss'] || '',
                width: 500,
                height: 300,
                readOnly: false,
                valign: 'bottom',
                align: 'center',
                buttons: {
                    'sure': function(){
                        var $form = $('#C160M01EForm');
                        if ($form.valid()) {
                            var selectBoss = $form.find('select[name^=boss]').map(function(){
                                return $(this).val();
                            }).toArray();
                            // check repeat
                            if (selectBoss.length ==
                            $.unique(selectBoss).length) {
                                pageAction.flow({
                                    activity: 'send'
                                });
                            }
                            else {
                                MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.cls1161m01['manager.repeat']);
                            }
                        }
                    },
                    'cancel': function(){
                        $.thickbox.close();
                    }
                }
            });
            }); 
        },
        Printpdf: function(){
            ilog.debug("exec js_function 'Printpdf' @ CLS1161M01Page.js");
            $.form.submit({
                url: "../../simple/FileProcessingService",
                target: "_blank",
                data: {
                    mainId: responseJSON.mainId,
                    fileDownloadName: "cls1161r01.pdf",
                    serviceName: "cls1161rptservice"
                }
            });
        }
    };

$(document).ready(function(){
    // Initialize page
    pageAction.init();
    pageAction.build();
});

function getCheckListPromptMsg(){
    var result = "";
    $.ajax({
        type: "POST",
        handler: 'cls1161formhandler',
        async: false,
        data: {
            formAction: "getPromptMsg",
            srcMainId: responseJSON.srcMainId
        },
        success: function(responseData){
            var PromptMsg = responseData.PromptMsg;
            if(PromptMsg != undefined && PromptMsg != null && PromptMsg != ""){
                result = PromptMsg;
            }				
        }
    });
    return result;	
}
