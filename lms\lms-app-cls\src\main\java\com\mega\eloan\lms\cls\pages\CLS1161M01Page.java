/* 
 * CLS1161M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.pages.CLS1161S02APage;
import com.mega.eloan.lms.cls.panels.CLS1161S01Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S02Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S03Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S04Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S05Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S20Panel;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 動用審核表
 * </pre>
 * 
 * @since 2012/12/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/25,Fantasy,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1161m01/{page}")
public class CLS1161M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Autowired
	CLS1161Service service;

	@Autowired
	CLSService clsService;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails ssoUserDetails = MegaSSOSecurityContext
		.getUserDetails();
		
		// 取得資料並確保參數正確設定
		String oid = Util.trim(params.getString(EloanConstants.OID));
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		String caseType = "";
		if (c160m01a != null) {
			caseType = Util.trim(c160m01a.getCaseType());
			// 確保 MAIN_OID 參數存在，用於權限檢核
			if (Util.isEmpty(params.getString(EloanConstants.MAIN_OID)) && c160m01a != null) {
				params.put(EloanConstants.MAIN_OID, c160m01a.getOid());
			}
			// UPGRADETODO: 同時設定 MAIN_ID 參數，供 CLS1131ServiceImpl.querySingleData 使用
			if (Util.isEmpty(params.getString(EloanConstants.MAIN_ID)) && c160m01a != null) {
				params.put(EloanConstants.MAIN_ID, c160m01a.getMainId());
			}
			// 設定前端所需的 mainId 參數
			model.addAttribute("mainId", c160m01a.getMainId());
		}
		
		// 依權限設定button - 使用文件狀態檢核
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CLSDocStatusEnum.編製中));
		addAclLabel(model,
				new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
						AuthType.Accept, CLSDocStatusEnum.待覆核,
						CLSDocStatusEnum.先行動用_待覆核));
		
		// UPGRADETODO: 修正重新上傳按鈕顯示邏輯 - 從單位特定改為編輯模式檢查
		// 原本邏輯：boolean isUnit900 = Util.equals(ssoUserDetails.getSsoUnitNo(), "900");
		// 修正後：重新上傳按鈕應該在編輯模式時顯示（文件狀態為「編製中」）
		boolean show_btnReUpDate = (c160m01a != null && 
				Util.equals(c160m01a.getDocStatus(), CLSDocStatusEnum.編製中.getCode()));
		model.addAttribute("_btnDOC_END_visible", show_btnReUpDate);
		// hide tab
		// 1.一般 2.團貸 3.整批匯入
		model.addAttribute("_caseType3_visible", caseType.matches("[12]"));

		// i18n
		renderJsI18N(CLS1161M01Page.class);
		renderJsI18N(CLS1161S02APage.class); // UPGRADETODO: 預載入 S02APage 的 i18n，確保動態載入時可用
		
		// UPGRADETODO: 使用標準載入方式，JavaScript 會在動態載入時按需載入
		model.addAttribute("loadScript", "loadScript('pagejs/cls/CLS1161M01Page');"); 
		
		// tabs
		int page = params.getAsInteger(EloanConstants.PAGE, 1);
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, caseType, c160m01a, ssoUserDetails);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
		// UPGRADETODO: 暫時用 HARDCODE 方式讓 IVR 頁籤強制顯示，方便測試
		// 原本邏輯：只有「特定金錢信託受益權自行設質擔保授信」專案才顯示 IVR 頁籤
		// 原本程式碼：service.getProjClassFromC160M01A(params.getString(EloanConstants.OID))
		// 完成測試後請改回原本邏輯
		model.addAttribute("show_ivr_panel_visible", true); // HARDCODE 強制顯示 IVR 頁籤
		model.addAttribute("show_queryBeforeMoneyAllocating_button",
				this.clsService.isOnlyFor027BranchUsage());
	}// ;

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index, String caseType
			, C160M01A c160m01a, MegaSSOUserDetails ssoUserDetails) {
		String active_SAS_AML = "0";
		if(c160m01a!=null && clsService.active_SAS_AML(c160m01a)){
			active_SAS_AML = clsService.is_aml_lockEdit(c160m01a.getMainId())?"2":"1";
		}		
		//=======================
		Panel panel = null;
		switch (index) {
		case 1:
			renderJsI18N(CLS1161S01Panel.class);
			panel = new CLS1161S01Panel(TAB_CTX, true, caseType,
					c160m01a.getNewCustFlag());
			break;
		case 2:
			renderJsI18N(CLS1161S02Panel.class);
			panel = new CLS1161S02Panel(TAB_CTX, true);
			break;
		case 3:
			C160M01C c160m01c = this.clsService.findC160M01C_mainId_itemCode_itemType(c160m01a.getMainId(), UtilConstants.C900S01B_ITEMCODE.第56項, UtilConstants.C900S01B_ITEMTYPE.共用項目);
			renderJsI18N(CLS1161S03Panel.class);
			panel = new CLS1161S03Panel(TAB_CTX, true,
					c160m01c == null ? false : true);
			break;
		case 4:
			renderJsI18N(CLS1161S04Panel.class);
			panel = new CLS1161S04Panel(TAB_CTX, true); // UPGRADETODO: 修復 Fragment 解析問題 - 增加 updatePanelName=true 參數
			break;
		case 5:
			panel = new CLS1201S20Panel(TAB_CTX, true, active_SAS_AML);
			renderJsI18N(CLS1201S20Panel.class);
			break;
		case 6:
			panel = new CLS1161S05Panel(TAB_CTX, true);
			break;
		default:
			renderJsI18N(CLS1161S01Panel.class);
			panel = new CLS1161S01Panel(TAB_CTX, true, caseType,
					c160m01a.getMainId());
			break;
		}
		if (panel == null) {
			panel = new Panel(TAB_CTX);
		}

		return panel;
	}

	// 驗證文件狀態Class
	public Class<? extends Meta> getDomainClass() {
		return C160M01A.class;
	}
}
