var panelAction = {
    handler: 'cls1161formhandler',
    data: {},
    init: function(){
    
    },
    build: function(){
        //全部收到/完成
        $('#C160M01CDiv').find('#allFinish').click(function(){
            $('#detials').find('select').each(function(){
                var $select = $(this);
                $select.val('1');
                var oid = $select.attr('oid') || '';
                panelAction.data[oid].itemCheck = '1';
            });
            $('table[selectType=1]').find('input').each(function(){
				if (!$(this).hasClass('required')) $(this).addClass('required');
	         });
             var $selecttype2 = $('table[selectType=2]');
             $selecttype2.find('input').each(function(){
                 if ($(this).val() == '') {
                     var oid = $(this).attr('oid');
                     $selecttype2.find('select[oid=' + oid + ']').val('0');
					 panelAction.data[oid].itemCheck = '0';
                 }
                 else {
                     if (!$(this).hasClass('required')) 
                         $(this).addClass('required');
                 }
             });
        })        //全部免附
        .end().find('#allWithout').click(function(){
            $('#detials').find('select').each(function(){
            	  var $select = $(this);
            	  $select.val('0');
                var oid = $select.attr('oid') || '';
                panelAction.data[oid].itemCheck = '0';
            });
            $('#detials').find('input').each(function(){
            	$(this).removeClass('required');
	          });
			  
        })        //重新引進
        .end().find('#pullinAgain').click(function(){
            MegaApi.confirmMessage(i18n.def["confirmRun"], function(action){
                if (action) {
                	var _action = "importC160M01C";
                	ilog.debug(panelAction.handler+" :: "+_action);
                    $.ajax({
                        handler: panelAction.handler,
                        action: _action,
                        formId: 'empty',
                        data: pageAction.getSaveData(),
                        success: function(response){
                            MegaApi.showPopMessage(i18n.def['confirmTitle'], i18n.def['runSuccess']);
                            pageAction.init();
                        },
                        error: function(xhr, status, error){
                            console.error('importC160M01C AJAX 錯誤:', status, error);
                            MegaApi.showPopMessage(i18n.def["confirmTitle"], "匯入檢附資訊失敗，請重試或聯絡資訊處!");
                        }
                    });
                }
            });
        })        //列印保管袋附件
        .end().find('#allPrint').click(function(){
            $.form.submit({
                url: "../../simple/FileProcessingService",
                target: "_blank",
                data: {
                    mainId: responseJSON.mainId,
                    fileDownloadName: "cls1161r02.pdf",
                    serviceName: "cls1161r02rptservice"
                }
            });
        })        //產生資料清冊
        .end().find('#allDetails').click(function(){

        })        //附加檔案
        .end().find('#addFiles').click(function(){
            if (typeof $.fn.size === 'undefined') {
                $.fn.size = function() {
                    return this.length;
                };
            }
            
            FilesAction.open({
                pid: responseJSON.mainId
            });
        })        //片語
        .end().find('#btnPhrase').click(function(){
        	if ($('#detials').find('select[value=2]').length > 0){
				panelAction.selectPhrase().done(function(returnObj){
					$('#comm').val( returnObj.r );					
				});
        	}else{
        		$('#comm').val(i18n.cls1161s03['C160M01A.importPhrase'] || '貸放手續齊全，擬准予動用');
        	}
        });
        
        //extend page action
        if (typeof pageAction !== 'undefined') {
            $.extend(pageAction, {
            check: function(){
                var s = new Date().getTime();
                var check = true;
                $('#detials').find('form').each(function(){
                    if (check) 
                        check = $(this).valid();
                });
                if (check) 
                	check = $('#C160M01AForm').valid();
                var e = new Date().getTime();
                //alert((e-s)/1000);
                return check;
            },
            getSaveData: function(){
                return {
                    detials: JSON.stringify(panelAction.data)
                };
            }
            });
        }
    },
    /**
     * 解析
     */
    parse: function(data, useProd69Fmt){
        ilog.debug("CLS1161S03Panel.js::parse(data, useProd69Fmt="+(useProd69Fmt||'')+") --- from CLS1161M01Page.js");
        
        // 如果 data 是字串，先解析成物件
        if (typeof data === 'string') {
            try {
                data = JSON.parse(data);
            } catch (e) {
                data = null;
            }
        }
        
    	$('#detials').empty();
        if (data) {
            var commonArray = [];
            var customArray = [];
            for (var i in data) {
                var json = data[i];
                if (json.itemType == '3') {
                    customArray.push(json);
                }
                else {
                    commonArray.push(json);
                }
            }
            var html = '<form >';
            if(useProd69Fmt=="Y"){
            	html += panelAction.parseTable_useProd69Fmt(commonArray,'1');
            }else{
            	html += panelAction.parseTable(commonArray,'1');	
            }            
            html += '<p><b>' + (i18n.cls1161s03['title.custom'] || '自行輸入項目') + '</b></p>';
            
            // 如果沒有自行輸入項目，建立一些空的項目
            if (customArray.length === 0) {
                for (var i = 0; i < 6; i++) {  // 建立6個空的自行輸入項目
                    customArray.push({
                        oid: 'custom_' + new Date().getTime() + '_' + i,
                        itemType: '3',
                        itemContent: '',
                        itemCheck: '0',
                        itemFormat: '[]',
                        itemDscr: ''
                    });
                }
            }
            
            if(useProd69Fmt=="Y"){
            	html += panelAction.parseTable_useProd69Fmt(customArray,'2');
            }else{
            	html += panelAction.parseTable(customArray,'2');
            }
            html += '</form>';
            
            $('#detials').html(html).buildItem();
            //itemCheck
            $('#detials').find('.itemCheck').change(function(){
                var oid = $(this).attr('oid') || '';
                var value = ($(this).val() || '') + '';
                var data = panelAction.data[oid];
                if (data) 
                    data.itemCheck = value;
                //為免時則不需檢查後面的份數欄位。(X和V時才要檢查) add by fantasy 2013/05/10
                $('#detials').find('input[oid="' + oid + '"]').each(function(){
                    if (value === '0') {
                        $(this).removeClass('required');
                    } else if (!$(this).hasClass('required')) {
                        $(this).addClass('required');
                    }
                });
            }) //itemContent
            .end().find('.itemContent').change(function(){
                var data = panelAction.data[$(this).attr('oid') || ''];
                if (data) {
                    data.itemContent = $(this).val() || '';
                    data.itemDscr = data.itemContent;
                }
            })            //itemFormat
            .end().find('.itemFormat').change(function(){
                var oid = $(this).attr('oid') || '';
                var array = [];
                var itemDscr = '';

                $('.' + DOMPurify.sanitize(oid)).each(function(){
                    var json = {
                        value: $(this).find('input:first-child').val() || '',
                        titleName: $(this).find('span').html() || ''
                    };
                    itemDscr += json.value + json.titleName;
                    array.push(json);
                });
                var data = panelAction.data[oid];
                if (data) {
                    data.itemFormat = array;
                    data.itemDscr = data.itemContent + itemDscr;
                }
            })
        } else {
            // 當資料為空時，仍然顯示「自行輸入項目」
            var html = '<form >';
            html += '<p><b>' + (i18n.cls1161s03['title.custom'] || '自行輸入項目') + '</b></p>';
            // 建立一個空的自行輸入項目
            var emptyCustomItem = [{
                oid: 'custom_' + new Date().getTime(),
                itemType: '3',
                itemContent: '',
                itemCheck: '0',
                itemFormat: '[]'
            }];
            html += panelAction.parseTable(emptyCustomItem,'2');
            html += '</form>';
            $('#detials').html(html).buildItem();
        }
    },
    parseTable_useProd69Fmt: function(args,count){
        var result = ''
        if (args) {
            result += '<table class="tb2" width="100%" selectType='+count+'>';
			var icount = 0;
            for (var i = 0; i < args.length; i ++) {
                result += ' <tr>';
                result += panelAction.parseTd(args[i], ++icount);
                result += ' </tr>';
            }
            result += '</table>';
        }
        return result;
    },
    parseTable: function(args,count){
        var result = ''
        if (args) {
            result += '<table class="tb2" width="100%" selectType='+count+'>';
			var icount = 0;
            for (var i = 0; i < args.length; i += 2) {
                result += ' <tr>';
                result += panelAction.parseTd(args[i], ++icount);
                result += panelAction.parseTd(args[i + 1], ++icount);
                result += ' </tr>';
            }
            result += '</table>';
        }
        return result;
    },
    parseTd: function(json, count){
        var result = '';
		
			var itemName ='';
		
        if (json && json.oid) {
            var oid = json.oid;
            panelAction.data[oid] = json;
			
			if (json.itemContent != null) {
				itemName= count + '、' + json.itemContent;
			}			
            
            result += '<td width="5%">';
            result += '  <select class="itemCheck" oid="' + oid + '"';
            result += '   codeType="cls1161m01_itemCheck" itemStyle="space:false;value:' + json.itemCheck + '" />';
            result += '</td>';
            result += '<td width="45%" >';
            switch (json.itemType + '') {
                case '3':
                    result += '<input type="text" class="itemContent" maxlength="60" size="50" '
                    result += ' value="' + (itemName || '') + '" oid="' + oid + '" />';
                    break;
                default:
                    result += (itemName || '');
                    break;
            }
            result += panelAction.parseFormat(oid, json);
            result += '</td>';
        }
        return result;
    },
    parseFormat: function(oid, source){
        var result = '';
        var agrs = source.itemFormat;
        
        // 如果 itemFormat 是字串，先解析成陣列
        if (typeof agrs === 'string' && agrs.trim() !== '') {
            // 檢測是否為malformed JSON (包含未加引號的屬性名)
            var needsFix = /\w+:/.test(agrs) && !/"\w+"/.test(agrs);
            
            if (needsFix) {
                // 直接修正格式後解析，避免不必要的錯誤訊息
                try {
                    var fixedJson = agrs
                        .replace(/(\w+):/g, '"$1":')  // 為屬性名加上引號
                        .replace(/'/g, '"');          // 將單引號替換為雙引號
                    agrs = JSON.parse(fixedJson);
                } catch (e) {
                    agrs = null;
                }
            } else {
                // 嘗試直接解析標準JSON
                try {
                    agrs = JSON.parse(agrs);
                } catch (e) {
                    agrs = null;
                }
            }
        }
        
        if (agrs && Array.isArray(agrs)) {
            for (var i in agrs) {
                var json = agrs[i];
                var id = parseInt(Math.random() * 100000, 10) + '';
                result += '<span class="' + oid + '">';
                result += '<input id="' + id + '" name="' + id + '" value="' + (json.value || '') + '" oid="' + oid + '"';
                result += ' size="1" class="number max '+(/[12]/.test(source.itemCheck) ? 'required' : '' ) +' itemFormat" maxlength="3" />';
                result += '<span>' + (json.titleName || '') + '</span>';
                result += '</span>'
            }
        }
        return result;
    },
    /**
     * 儲存
     */
    save: function(){
    
    },	
	selectPhrase: function(){
		var my_dfd = $.Deferred();
		
		var _id = "_div_btn_Phrase";
		var _form = _id + "_form";
		if ($("#" + _id).length == 0) {
			var dyna = [];
			dyna.push("<div id='" + _id + "' style='display:none;' >");
			dyna.push("<form id='" + _form + "'>");
			
			var submenu = {
				'1': i18n.cls1161s03["C160M01A.importPhrase1"],
				'2': i18n.cls1161s03["C160M01A.importPhrase2"]
			};
			build_submenu(dyna, 'decision_Phrase', submenu);
			
			dyna.push("</form>");
			dyna.push("</div>");
			
			$('body').append(dyna.join(""));
		}
		
		//clear data
		$("#" + _form).reset();
		
		$("#" + _id).thickbox({ // 使用選取的內容進行彈窗
			title: "",
			width: 380,
			height: 250,
			align: "center",
			valign: "bottom",
			modal: false,
			i18n: i18n.def,
			buttons: {
				"sure": function(){
					if (!$("#" + _form).valid()) {
						return;
					}
					var val = $("#" + _form).find("[name='decision_Phrase']:checked").val();
					$.thickbox.close();
					
					if (val == "1") {
						my_dfd.resolve( {'r':i18n.cls1161s03["C160M01A.importPhrase1"] } );
					}else if (val == "2") {
						my_dfd.resolve( {'r':i18n.cls1161s03["C160M01A.importPhrase2"] } );
					}else{
						my_dfd.reject();
					}
				},
				"cancel": function(){
					$.thickbox.close();
				}
			}
		});
		return my_dfd.promise();
	}
}

$(function(){
    panelAction.build();
    panelAction.init();
});
$("#allDetails").click(function(){
    $('#openC801M01A').thickbox({
        title: i18n.cls1161s03['C801M01A.title'],// C801M01A.title=個金個人資料清冊
        width: 800,
        height: 300,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            'close': function(){
                $.thickbox.close();
            }
        }
    });
});

function build_submenu(dyna, rdoName, submenu){
	$.each(submenu, function(k, v) { 
		dyna.push("   <p ><label id='_itemMenu_"+rdoName+"_"+k+"'><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>"); 
    });		
}

/**
 * 開啟文件
 */
var openDoc = function(cellvalue, options, rowObject){

	$.form.submit({
			url:'../cls8011m01/01',
			data:{
				mainOid: rowObject.oid,
				mainId: rowObject.mainId,
            mainDocStatus: rowObject.docStatus
			},
			target:rowObject.oid
	});

}
	
var grid = $("#C801M01AGrid").iGrid({
    height: 90,
    handler: "cls8011gridhandler",
    action: "queryC801M01A",
    rownumbers: true,
    colModel: [{		
        width: 40, // 設定寬度
        colHeader: i18n.cls1161s03['C801M01A.caseNo'],//C801M01A.caseNo=檔案編號
        name: "caseNo",
        align: "center"
    }, {
        width: 30, // 設定寬度
        colHeader: i18n.cls1161s03['C801M01A.custId'],//C801M01A.custId=統一編號
        name: "custId",
        align: "center",
        formatter: 'click',
        onclick: openDoc
    }, {
        width: 20, // 設定寬度
        colHeader: i18n.cls1161s03['C801M01A.custName'],//C801M01A.custName=客戶名稱
        name: "custName",
        align: "center"
    }, {
        width: 30, // 設定寬度
        colHeader: i18n.cls1161s03['C801M01A.cntrNo'],//C801M01A.cntrNo=額度序號
        name: "cntrNo",
        align: "center",
        formatter: 'click',
        onclick: openDoc
    }, {
        width: 40, // 設定寬度
        colHeader: i18n.cls1161s03['C801M01A.loanNo'],//C801M01A.loanNo=放款帳號
        name: "loanNo",
        align: "center"
    }, {
        name: "oid",
        hidden: "true"
	}, {
        name: "mainId",
        hidden: "true"
    }, {
        name: "docStatus",
        hidden: "true"
    }],
    ondblClickRow: function(rowid){
        var data = grid.getRowData(rowid);
        openDoc(null, null, data);
    }
});

//重新弔進
$("#pullinC801M01ABt").click(function(){
	$.ajax({
        handler: 'cls1161formhandler',
        action: 'getC801M01AByL140M01A',
        success: function(response){
			$("#C801M01AGrid").trigger("reloadGrid");
            MegaApi.showPopMessage(i18n.def['confirmTitle'], i18n.def['runSuccess']);            
        },
        error: function(xhr, status, error){
            console.error('getC801M01AByL140M01A AJAX 錯誤:', status, error);
            MegaApi.showPopMessage(i18n.def["confirmTitle"], "重新弔進失敗，請重試或聯絡資訊處!");
        }
    });
});

$("#delC801M01ABt").click(function(){
	var data = grid.getSingleData();
    if (data) {    	
        MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
            if (action) {
            	var oids = [];
            	oids.push(data.oid);
            	
                $.ajax({
                    handler: 'cls8011m01formhandler',
                    action: 'deleteC801m01a',
                    data: {
                    	'oids':oids
                    },
                    success: function(response){
                    	$("#C801M01AGrid").trigger("reloadGrid");
                        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                    },
                    error: function(xhr, status, error){
                        console.error('deleteC801m01a AJAX 錯誤:', status, error);
                        MegaApi.showPopMessage(i18n.def["confirmTitle"], "刪除失敗，請重試或聯絡資訊處!");
                    }
                });
            }
        });
    }
});   