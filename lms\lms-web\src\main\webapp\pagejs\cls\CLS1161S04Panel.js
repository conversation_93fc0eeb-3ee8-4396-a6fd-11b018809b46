var panelAction = {
	handler : 'cls1161formhandler',
	init : function(){
		// UPGRADETODO: 初始化先行動用頁籤的相關功能
		var $div = $('#C160M01DDiv');
		if ($div.length > 0) {
			// 建立下拉選單和其他UI元件
			$div.buildItem();
			console.log('CLS1161S04Panel 初始化完成');
		} else {
			console.warn('CLS1161S04Panel: 找不到 C160M01DDiv 容器');
		}
	},
	build : function(){
		// UPGRADETODO: 建立先行動用頁籤的UI元件和事件處理
		
		//extend page action - 檢查 pageAction 是否已定義才進行擴展
		if (typeof pageAction !== 'undefined') {
			$.extend(pageAction, {
				check : function(){
					// UPGRADETODO: 修正表單ID，確保對應到實際的表單元素
					var $form = $('#C160M01DForm');
					if ($form.length > 0) {
						return $form.valid();
					}
					// 如果找不到表單，檢查是否有其他表單
					var $tabForm = $('#tabForm');
					if ($tabForm.length > 0) {
						return $tabForm.valid();
					}
					// 預設回傳 true，避免阻止其他頁籤的儲存
					return true;
				},
				getSaveData : function(){
					// UPGRADETODO: 修正表單資料的取得方式
					var $form = $('#C160M01DForm');
					if ($form.length > 0) {
						return {
							C160M01DForm : JSON.stringify($form.serializeData())
						};
					}
					// 如果找不到 C160M01DForm，嘗試使用 tabForm
					var $tabForm = $('#tabForm');
					if ($tabForm.length > 0) {
						return {
							C160M01AForm : JSON.stringify($tabForm.serializeData())
						};
					}
					// 回傳空物件，避免錯誤
					return {};
				}
			});
		}
		// UPGRADETODO: 如果 pageAction 未定義，表示主頁面尚未載入，這是正常的載入順序
	}
}

$(function() {
	panelAction.build();
	panelAction.init();
});